package com.dbg.p10dz.utils;

import android.content.Context;
import android.content.SharedPreferences;

public class SPUtils {
    private static final String SP_NAME = "aging_test_settings";
    private static final String KEY_AGING_LEVEL = "aging_level";
    private static final String KEY_AGING_DURATION = "aging_duration";
    
    // 默认值
    private static final boolean DEFAULT_LEVEL = false;  // false 表示低强度
    private static final long DEFAULT_DURATION = 480;    // 默认8小时
    
    private static SharedPreferences getSharedPreferences(Context context) {
        return context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE);
    }
    
    // 保存老化等级
    public static void saveAgingLevel(Context context, boolean isHighLevel) {
        getSharedPreferences(context)
            .edit()
            .putBoolean(KEY_AGING_LEVEL, isHighLevel)
            .apply();
    }
    
    // 获取老化等级
    public static boolean getAgingLevel(Context context) {
        return getSharedPreferences(context)
            .getBoolean(KEY_AGING_LEVEL, DEFAULT_LEVEL);
    }
    
    // 保存老化时长
    public static void saveAgingDuration(Context context, long durationMinutes) {
        getSharedPreferences(context)
            .edit()
            .putLong(KEY_AGING_DURATION, durationMinutes)
            .apply();
    }
    
    // 获取老化时长
    public static long getAgingDuration(Context context) {
        return getSharedPreferences(context)
            .getLong(KEY_AGING_DURATION, DEFAULT_DURATION);
    }
} 