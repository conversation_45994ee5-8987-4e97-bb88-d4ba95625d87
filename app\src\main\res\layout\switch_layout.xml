<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical">

    <Button
        android:id="@+id/btn_aging_test"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="老化"
        android:textSize="24sp"
        android:layout_marginEnd="28dp"
        android:textColor="@android:color/white"
        style="@style/AgingButtonStyle"/>

    <TextView
        android:id="@+id/tv_scanner_switch"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="扫码头"
        android:layout_marginEnd="8dp"
        android:textColor="@android:color/white"/>

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/action_switch"
        android:layout_marginEnd="18dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</LinearLayout>
