package com.dbg.p10dz;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

public class BtActivity extends AppCompatActivity {
    private static final String TAG = "BtActivity";
    private BtManager btManager;
    private SystemBtManager systemBtManager;

    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bluetoothtest);
        TextView textView = findViewById(R.id.bluetoothTextView);

        // 检查是否为系统应用
        systemBtManager = new SystemBtManager(textView);
        if (systemBtManager.isSystemApp()) {
            Log.d(TAG, "检测到系统应用，使用无感连接模式");
            textView.append("系统应用模式 - 无感连接启动\n");
            // 系统应用直接开始无感连接流程
            systemBtManager.initSystemBluetoothFlow(this);
        } else {
            Log.d(TAG, "普通应用，使用传统权限请求模式");
            textView.append("普通应用模式 - 需要用户授权\n");
            // 普通应用使用原有的权限请求流程
            initNormalAppFlow(textView);
        }
    }

    /**
     * 普通应用的权限请求流程（保持原有逻辑）
     */
    private void initNormalAppFlow(TextView textView) {
        String[] permissions;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions = new String[]{
                    android.Manifest.permission.BLUETOOTH_CONNECT,
                    android.Manifest.permission.BLUETOOTH_SCAN,
                    android.Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
            };
        } else {
            permissions = new String[]{
                    android.Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
            };
        }

        // 检查并请求所有未授权的权限
        boolean allGranted = true;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                allGranted = false;
                break;
            }
        }

        if (!allGranted) {
            ActivityCompat.requestPermissions(this, permissions, 100);
        }

        btManager = new BtManager(textView);
        btManager.initBluetoothLauncher(this);
        if (allGranted) {
            btManager.checkAndStartBluetoothFlow(this);
        }
    }

    public void onBackPressed(){
        // 根据当前使用的管理器来处理
        if (systemBtManager != null && systemBtManager.isSystemApp()) {
            systemBtManager.stopMusic();
            systemBtManager.unpairDevice();
        } else if (btManager != null) {
            btManager.stopMusic();
            btManager.unpairDevice();
        }
        super.onBackPressed(); // 正常返回
    }
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 100) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }
            if (allGranted && btManager != null) {
                btManager.checkAndStartBluetoothFlow(this);
            }
        }
    }

    protected void onDestroy() {
        super.onDestroy();
        // 根据当前使用的管理器来释放资源
        if (systemBtManager != null && systemBtManager.isSystemApp()) {
            systemBtManager.release();
        } else if (btManager != null) {
            btManager.release();
        }
    }
}

