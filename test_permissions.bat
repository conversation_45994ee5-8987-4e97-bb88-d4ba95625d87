@echo off
echo ========================================
echo P10DZ 权限测试脚本
echo ========================================
echo.

echo 1. 检查设备连接状态...
adb devices
echo.

echo 2. 检查应用是否已安装...
adb shell pm list packages | findstr com.dbg.p10dz
echo.

echo 3. 检查应用权限状态...
echo --- 危险权限状态 ---
adb shell pm list permissions -d -g com.dbg.p10dz
echo.

echo 4. 检查具体权限授予情况...
echo [蓝牙扫描权限]
adb shell pm list permissions -g com.dbg.p10dz | findstr BLUETOOTH_SCAN
echo [蓝牙连接权限]
adb shell pm list permissions -g com.dbg.p10dz | findstr BLUETOOTH_CONNECT
echo [位置权限]
adb shell pm list permissions -g com.dbg.p10dz | findstr ACCESS_FINE_LOCATION
echo.

echo 5. 检查应用安装位置...
adb shell pm path com.dbg.p10dz
echo.

echo 6. 检查应用系统标志...
adb shell dumpsys package com.dbg.p10dz | findstr -i "system\|priv"
echo.

echo 7. 手动授予权限（如果需要）...
echo 如果上述权限显示为未授予，可以手动执行以下命令：
echo adb shell pm grant com.dbg.p10dz android.permission.BLUETOOTH_SCAN
echo adb shell pm grant com.dbg.p10dz android.permission.BLUETOOTH_CONNECT
echo adb shell pm grant com.dbg.p10dz android.permission.ACCESS_FINE_LOCATION
echo.

echo 8. 启动应用进行测试...
adb shell am start -n com.dbg.p10dz/.MainActivity
echo.

echo 9. 查看应用日志...
echo 请在另一个命令行窗口中运行以下命令查看日志：
echo adb logcat -s PrivPermissionGranter MainActivity
echo.

echo ========================================
echo 测试完成！请查看上述输出结果。
echo ========================================
pause
