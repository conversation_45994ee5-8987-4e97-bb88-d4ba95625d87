-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:2:1-81:12
INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:2:1-81:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3426cb7fb5e8d3a56c3156e2c883a43a\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c476fde4462ba7079004fd4b4e721aa\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c4e224b6c9906927c14023bac0ddcd49\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\91810e51fae1f6a732aa4e974bbeb5d0\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef323918cb8f25dd93231714e2f655f5\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\82a2b46c327c1e8496a7fe21c9aae5df\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\0fd4953d5e915dfa84007836b7b5a9eb\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e35fe96c89b16a775668fb394aa3b7c\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1abbca3b234f35cfa7add7b66f6059b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cef06a70a697cdd64a373b5a490e774\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bad4cd2b64510adf2a135bc3cf23f2d1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c53b5ed83e45ed9ac2b25a6668a1affb\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\87d855518ba8b632ab26b4e7b560e323\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\75594d7710b5f2be40d82e0464331794\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f7045f69978d948f3b4e969892b1ac4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e50941328315b5a8c860d9b1f149b55\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d461a1e38b2b82c0fc1dadb5da679836\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\91cf67423456dc035e3a7b0d8ffe0fa2\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c2d66160a23c05028e560f9be1c671dc\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5ca846e595f75d4b244ac377fc6c8e5\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\201c8032d1a6eb8113fe4a28597a66f3\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\82b620be4517ad2d9fc4198226c240c0\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6682fe48a5928a6bce96c08c3e40191a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3061c3b5fd2304ed915da4fce69f2970\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8903b99cd70fbfad2ddc78e0146b0d2c\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\49158a41b279628200d903d50c6a05de\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e01d2afa2c338b50bfc8e96f444f4d89\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e797a32cc44fa2e4bca9041dff628b98\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\57ade86e645217283c2f14e98c480305\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1675bdc1bc56afb5c8fe766d00deb137\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\16666e932753c1bf3d4cd12c00f41b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad5822c8a2db1c313452a9f5034bbd04\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94949e73ab635ca5f251519a5d6656da\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\80e4471c3dd816ff8a16cd10953ae982\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2bd0998c4caf01cd3d4e52967e302406\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e8ca4a60570482a3a9dfcce1698883da\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\526e0d2336621afa65fdf3004259e5ef\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\23f932108863f99915c386518f1cd545\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:5:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:7:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:7:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:8:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:8:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:9:5-10:58
	android:usesPermissionFlags
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:10:9-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:9:22-73
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:11:5-12:58
	android:usesPermissionFlags
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:12:9-55
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:11:22-70
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:13:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:13:22-75
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:16:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:16:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:17:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:17:22-78
uses-permission#android.permission.BLUETOOTH_PRIVILEGED
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:20:5-21:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:21:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:20:22-76
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:25:5-66
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:25:22-64
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:26:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:26:22-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:27:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:27:22-78
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:28:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:28:22-77
uses-permission#android.permission.WRITE_SETTINGS
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:29:5-30:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:30:9-44
	android:required
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:29:71-94
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:29:22-70
uses-permission#android.permission.USB_PERMISSION
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:31:5-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:31:22-70
uses-permission#android.permission.USB_DEVICE_ADMIN
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:32:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:32:22-72
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:33:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:33:22-68
application
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:34:5-80:19
INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:34:5-80:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3426cb7fb5e8d3a56c3156e2c883a43a\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3426cb7fb5e8d3a56c3156e2c883a43a\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c4e224b6c9906927c14023bac0ddcd49\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c4e224b6c9906927c14023bac0ddcd49\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1675bdc1bc56afb5c8fe766d00deb137\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1675bdc1bc56afb5c8fe766d00deb137\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\16666e932753c1bf3d4cd12c00f41b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\16666e932753c1bf3d4cd12c00f41b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:40:9-54
	android:icon
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:38:9-43
	android:networkSecurityConfig
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:43:9-69
	android:sharedUserId
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:45:9-50
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:41:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:39:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:37:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:46:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:35:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:44:9-43
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:36:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:42:9-44
activity#com.dbg.p10dz.MainActivity
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:47:9-59:20
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:48:13-48
	android:exported
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:50:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:49:13-41
intent-filter#action:name:android.hardware.usb.action.USB_DEVICE_ATTACHED+action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:51:13-55:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:52:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:52:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:53:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:53:27-74
action#android.hardware.usb.action.USB_DEVICE_ATTACHED
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:54:17-90
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:54:25-87
meta-data#android.hardware.usb.action.USB_DEVICE_ATTACHED
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:56:13-58:57
	android:resource
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:58:17-54
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:57:17-79
activity#com.dbg.p10dz.AgingTestActivity
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:61:9-66:47
	android:label
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:63:13-33
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:66:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:64:13-37
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:65:13-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:62:13-46
activity#com.dbg.p10dz.NetworkStressTestActivity
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:68:9-73:76
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:72:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:70:13-37
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:73:13-74
	android:taskAffinity
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:71:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:69:13-54
activity#com.dbg.p10dz.BtActivity
ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:75:9-79:76
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:78:13-44
	android:exported
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:76:13-37
	android:configChanges
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:79:13-74
	android:taskAffinity
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:77:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:75:19-45
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3426cb7fb5e8d3a56c3156e2c883a43a\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3426cb7fb5e8d3a56c3156e2c883a43a\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c476fde4462ba7079004fd4b4e721aa\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c476fde4462ba7079004fd4b4e721aa\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c4e224b6c9906927c14023bac0ddcd49\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c4e224b6c9906927c14023bac0ddcd49\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\91810e51fae1f6a732aa4e974bbeb5d0\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\91810e51fae1f6a732aa4e974bbeb5d0\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef323918cb8f25dd93231714e2f655f5\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.9\transforms\ef323918cb8f25dd93231714e2f655f5\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\82a2b46c327c1e8496a7fe21c9aae5df\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\82a2b46c327c1e8496a7fe21c9aae5df\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\0fd4953d5e915dfa84007836b7b5a9eb\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\0fd4953d5e915dfa84007836b7b5a9eb\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e35fe96c89b16a775668fb394aa3b7c\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6e35fe96c89b16a775668fb394aa3b7c\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1abbca3b234f35cfa7add7b66f6059b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\f1abbca3b234f35cfa7add7b66f6059b\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cef06a70a697cdd64a373b5a490e774\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cef06a70a697cdd64a373b5a490e774\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bad4cd2b64510adf2a135bc3cf23f2d1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bad4cd2b64510adf2a135bc3cf23f2d1\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c53b5ed83e45ed9ac2b25a6668a1affb\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c53b5ed83e45ed9ac2b25a6668a1affb\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\87d855518ba8b632ab26b4e7b560e323\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\87d855518ba8b632ab26b4e7b560e323\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\75594d7710b5f2be40d82e0464331794\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\75594d7710b5f2be40d82e0464331794\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f7045f69978d948f3b4e969892b1ac4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f7045f69978d948f3b4e969892b1ac4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e50941328315b5a8c860d9b1f149b55\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e50941328315b5a8c860d9b1f149b55\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d461a1e38b2b82c0fc1dadb5da679836\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d461a1e38b2b82c0fc1dadb5da679836\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\91cf67423456dc035e3a7b0d8ffe0fa2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\91cf67423456dc035e3a7b0d8ffe0fa2\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c2d66160a23c05028e560f9be1c671dc\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\c2d66160a23c05028e560f9be1c671dc\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5ca846e595f75d4b244ac377fc6c8e5\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f5ca846e595f75d4b244ac377fc6c8e5\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\201c8032d1a6eb8113fe4a28597a66f3\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\201c8032d1a6eb8113fe4a28597a66f3\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\82b620be4517ad2d9fc4198226c240c0\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\82b620be4517ad2d9fc4198226c240c0\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6682fe48a5928a6bce96c08c3e40191a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\6682fe48a5928a6bce96c08c3e40191a\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3061c3b5fd2304ed915da4fce69f2970\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\3061c3b5fd2304ed915da4fce69f2970\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8903b99cd70fbfad2ddc78e0146b0d2c\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8903b99cd70fbfad2ddc78e0146b0d2c\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\49158a41b279628200d903d50c6a05de\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\49158a41b279628200d903d50c6a05de\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e01d2afa2c338b50bfc8e96f444f4d89\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e01d2afa2c338b50bfc8e96f444f4d89\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e797a32cc44fa2e4bca9041dff628b98\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e797a32cc44fa2e4bca9041dff628b98\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\57ade86e645217283c2f14e98c480305\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\57ade86e645217283c2f14e98c480305\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1675bdc1bc56afb5c8fe766d00deb137\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1675bdc1bc56afb5c8fe766d00deb137\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\16666e932753c1bf3d4cd12c00f41b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\16666e932753c1bf3d4cd12c00f41b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad5822c8a2db1c313452a9f5034bbd04\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad5822c8a2db1c313452a9f5034bbd04\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94949e73ab635ca5f251519a5d6656da\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\94949e73ab635ca5f251519a5d6656da\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\80e4471c3dd816ff8a16cd10953ae982\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\80e4471c3dd816ff8a16cd10953ae982\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2bd0998c4caf01cd3d4e52967e302406\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2bd0998c4caf01cd3d4e52967e302406\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e8ca4a60570482a3a9dfcce1698883da\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e8ca4a60570482a3a9dfcce1698883da\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\526e0d2336621afa65fdf3004259e5ef\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\526e0d2336621afa65fdf3004259e5ef\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\23f932108863f99915c386518f1cd545\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\23f932108863f99915c386518f1cd545\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\16666e932753c1bf3d4cd12c00f41b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\16666e932753c1bf3d4cd12c00f41b25\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.dbg.p10dz.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.dbg.p10dz.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
