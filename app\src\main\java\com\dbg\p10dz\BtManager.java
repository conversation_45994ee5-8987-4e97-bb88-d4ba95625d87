package com.dbg.p10dz;

import android.Manifest;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.AssetFileDescriptor;
import android.media.MediaPlayer;
import android.os.Build;
import android.util.Log;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.lang.reflect.Method;
import java.util.Set;

public class BtManager {
    private TextView textView;
    private Context context;
    private MediaPlayer mediaPlayer;
    private BluetoothAdapter bluetoothAdapter;
    private ActivityResultLauncher<Intent> bluetoothLauncher;
    private static final String TARGET_DEVICE_NAME = "BlueCash-05 cradle";
    private final BroadcastReceiver bluetoothReceiver = new BroadcastReceiver() {
        @SuppressLint("MissingPermission")
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                if (device != null && TARGET_DEVICE_NAME.equals(device.getName())) {
                    Log.e("lyc---", "找到目标" + device.getName());
                    bluetoothAdapter.cancelDiscovery();
                    device.createBond();//找到名字，创建绑定
                }
                updateTextView(device);//更新设备显示
            } else if (BluetoothDevice.ACTION_BOND_STATE_CHANGED.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                int bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, -1);
                if (device != null && TARGET_DEVICE_NAME.equals(device.getName())) {
                    if (bondState == BluetoothDevice.BOND_BONDED) {
                        Log.e("lyc---", "配对成功，播放音乐");
                        playMusic();
                    }
                }
            }
        }
    };

    public BtManager(TextView textView) {
        this.textView = textView;
    }


    public void initBluetoothLauncher(AppCompatActivity activity) {
        this.context = activity;
        // 注册蓝牙开启结果回调
        bluetoothLauncher = activity.registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(), result -> {
                    if (result.getResultCode() == AppCompatActivity.RESULT_OK) {
                        Log.e("lyc--", "用户开启蓝牙");
                        if (isTargetDeviceBonded()) {
                            Log.e("lyc--", "目标设备已配对，直接播放音乐");
                            playMusic();
                        } else {
                            startScan();
                        }
                    } else {
                        Log.e("lyc--", "用户拒绝开启");
                    }
                });
    }

    public void checkAndStartBluetoothFlow(AppCompatActivity activity) {
        this.context = activity;
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

        if (bluetoothAdapter == null) {
            Log.e("lyc--", "设备不支持蓝牙");
            return;
        }

        // 注册广播
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        activity.registerReceiver(bluetoothReceiver, filter);

        // 如果已有权限则继续流程，否则请求权限
        if (hasAllPermissions(activity)) {
            startBluetoothFlow();
        }
    }

    //蓝牙未开启，请求开启
    public void startBluetoothFlow() {
        if (!bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            bluetoothLauncher.launch(enableBtIntent); //启动蓝牙
        } else {
            if (isTargetDeviceBonded()) {
                Log.e("lyc--", "目标设备已配对，直接播放音乐");
                playMusic();
            } else {
                startScan();// 开始扫描
            }

        }
    }


    @SuppressLint("MissingPermission")
    private boolean isTargetDeviceBonded() {  //判断是否配对上
        Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
        for (BluetoothDevice device : bondedDevices) {
            if (TARGET_DEVICE_NAME.equals(device.getName())) {
                return true;
            }
        }
        return false;
    }


    @SuppressLint("MissingPermission")
    private void startScan() {  //开始扫描
        if (bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
        Log.e("lyc--", "开始扫描设备");
        bluetoothAdapter.startDiscovery();
    }

    private void updateTextView(BluetoothDevice device) {  //更新扫描蓝牙设备显示
        if (device != null) {
            @SuppressLint("MissingPermission")
            String deviceName = device.getName();
            Log.e("lyc--", "扫描到设备" + deviceName);
            textView.append("找到设备" + deviceName + "\n");

        }
    }

    private void playMusic() {  //播放音乐
        try {
            if (mediaPlayer != null) {
                mediaPlayer.release();
                mediaPlayer = null;
            }
            mediaPlayer = new MediaPlayer();
            AssetFileDescriptor afd = context.getResources().openRawResourceFd(R.raw.gee);
            mediaPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
            afd.close();

            mediaPlayer.prepare();
            mediaPlayer.start();

            mediaPlayer.setOnCompletionListener(mp -> {
                Log.e("lyc--", "播放完成，释放资源");
                stopMusic();
                unpairDevice();
            });
        } catch (Exception e) {
            Log.e("lyc--", "播放音乐失败", e);
        }
    }

    public void stopMusic() {
        if (mediaPlayer != null) {
            mediaPlayer.stop();
            mediaPlayer.release();
            mediaPlayer = null;
        }
    }

    @SuppressLint("MissingPermission")
    public void unpairDevice() {
        Set<BluetoothDevice> bonedDevices = bluetoothAdapter.getBondedDevices();
        for (BluetoothDevice device : bonedDevices) {
            if (TARGET_DEVICE_NAME.equals(device.getName())) {
                try {
                    Method removeBondMethod = device.getClass().getMethod("removeBond");
                    removeBondMethod.invoke(device);
                    Log.d("lyc---", "取消配对成功");
                } catch (Exception e) {
                    Log.d("lyc---", "取消配对失败", e);
                }
                break;


            }
        }
        if (bluetoothAdapter.isEnabled()) {
            bluetoothAdapter.disable();
            Log.d("lyc---", "蓝牙已关闭");

        }
    }

    private boolean hasAllPermissions(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED &&
                    ContextCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED &&
                    ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        } else {
            return ContextCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        }
    }

    public void release() {
        try {
            context.unregisterReceiver(bluetoothReceiver);

        } catch (Exception e) {
            Log.d("lyc---", "广播取消注册失败", e);
        }
        stopMusic();
    }


}
