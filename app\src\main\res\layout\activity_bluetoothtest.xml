<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#000000"
    android:padding="20dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:textSize="30dp"
        android:textColor="@color/white"
        android:text="喇叭测试"/>




    <TextView
        android:id="@+id/tv_loudspeaker"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="160dp"
        android:text="蓝牙连接喇叭测试中...."
        android:textColor="@color/white"
        android:textSize="25dp" />

   <TextView
       android:id="@+id/bluetoothTextView"
       android:layout_width="wrap_content"
       android:layout_height="wrap_content"
       android:text="蓝牙扫描设备："/>

</LinearLayout>