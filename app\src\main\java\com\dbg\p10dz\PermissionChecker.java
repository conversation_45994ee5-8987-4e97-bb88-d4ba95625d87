package com.dbg.p10dz;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.util.Log;
import android.widget.Toast;

/**
 * 权限检查工具类
 * 用于检查和显示应用权限状态
 */
public class PermissionChecker {
    
    private static final String TAG = "PermissionChecker";
    
    /**
     * 需要检查的关键权限
     */
    private static final String[] CRITICAL_PERMISSIONS = {
        Manifest.permission.BLUETOOTH_SCAN,
        Manifest.permission.BLUETOOTH_CONNECT,
        Manifest.permission.BLUETOOTH_ADVERTISE,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
        Manifest.permission.RECORD_AUDIO
    };
    
    /**
     * 权限中文名称映射
     */
    private static final String[] PERMISSION_NAMES = {
        "蓝牙扫描",
        "蓝牙连接", 
        "蓝牙广播",
        "精确位置",
        "大致位置",
        "录音"
    };
    
    /**
     * 检查所有关键权限状态
     * @param activity 当前Activity
     * @return 是否所有权限都已授予
     */
    public static boolean checkAllPermissions(Activity activity) {
        Log.i(TAG, "=== 开始检查关键权限状态 ===");
        
        boolean allGranted = true;
        StringBuilder report = new StringBuilder();
        report.append("权限检查报告:\n");
        
        for (int i = 0; i < CRITICAL_PERMISSIONS.length; i++) {
            String permission = CRITICAL_PERMISSIONS[i];
            String name = PERMISSION_NAMES[i];
            
            boolean isGranted = activity.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED;
            String status = isGranted ? "✓ 已授予" : "✗ 未授予";
            
            Log.i(TAG, name + " (" + permission + "): " + status);
            report.append(name).append(": ").append(status).append("\n");
            
            if (!isGranted) {
                allGranted = false;
            }
        }
        
        // 检查应用类型
        boolean isSystemApp = isSystemApp(activity);
        Log.i(TAG, "应用类型: " + (isSystemApp ? "系统应用" : "普通应用"));
        report.append("应用类型: ").append(isSystemApp ? "系统应用" : "普通应用").append("\n");
        
        Log.i(TAG, "=== 权限检查完成 ===");
        Log.i(TAG, "总体状态: " + (allGranted ? "所有权限已授予" : "存在未授予的权限"));
        
        return allGranted;
    }
    
    /**
     * 显示权限状态Toast
     * @param activity 当前Activity
     */
    public static void showPermissionStatusToast(Activity activity) {
        boolean allGranted = checkAllPermissions(activity);
        
        String message;
        if (allGranted) {
            message = "✓ 所有关键权限已授予，功能正常";
        } else {
            message = "⚠ 部分权限未授予，可能影响功能使用";
        }
        
        Toast.makeText(activity, message, Toast.LENGTH_LONG).show();
    }
    
    /**
     * 检查蓝牙相关权限
     * @param activity 当前Activity
     * @return 蓝牙权限是否完整
     */
    public static boolean checkBluetoothPermissions(Activity activity) {
        Log.i(TAG, "=== 检查蓝牙权限 ===");
        
        boolean scanGranted = activity.checkSelfPermission(Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED;
        boolean connectGranted = activity.checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED;
        boolean advertiseGranted = activity.checkSelfPermission(Manifest.permission.BLUETOOTH_ADVERTISE) == PackageManager.PERMISSION_GRANTED;
        
        Log.i(TAG, "蓝牙扫描权限: " + (scanGranted ? "已授予" : "未授予"));
        Log.i(TAG, "蓝牙连接权限: " + (connectGranted ? "已授予" : "未授予"));
        Log.i(TAG, "蓝牙广播权限: " + (advertiseGranted ? "已授予" : "未授予"));
        
        boolean allBluetoothGranted = scanGranted && connectGranted && advertiseGranted;
        Log.i(TAG, "蓝牙权限状态: " + (allBluetoothGranted ? "完整" : "不完整"));
        
        return allBluetoothGranted;
    }
    
    /**
     * 检查位置权限
     * @param activity 当前Activity
     * @return 位置权限是否已授予
     */
    public static boolean checkLocationPermissions(Activity activity) {
        Log.i(TAG, "=== 检查位置权限 ===");
        
        boolean fineLocationGranted = activity.checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        boolean coarseLocationGranted = activity.checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        
        Log.i(TAG, "精确位置权限: " + (fineLocationGranted ? "已授予" : "未授予"));
        Log.i(TAG, "大致位置权限: " + (coarseLocationGranted ? "已授予" : "未授予"));
        
        // 至少需要一个位置权限
        boolean hasLocationPermission = fineLocationGranted || coarseLocationGranted;
        Log.i(TAG, "位置权限状态: " + (hasLocationPermission ? "已授予" : "未授予"));
        
        return hasLocationPermission;
    }
    
    /**
     * 检查是否为系统应用
     */
    private static boolean isSystemApp(Context ctx) {
        int flags = ctx.getApplicationInfo().flags;
        boolean isSystem = (flags & android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0;
        
        // 尝试检查特权应用标志
        boolean isPrivileged = false;
        try {
            java.lang.reflect.Field privilegedField = android.content.pm.ApplicationInfo.class.getField("FLAG_PRIVILEGED");
            int privilegedFlag = privilegedField.getInt(null);
            isPrivileged = (flags & privilegedFlag) != 0;
        } catch (Exception e) {
            // 忽略异常，可能是较老的Android版本
        }
        
        return isSystem || isPrivileged;
    }
    
    /**
     * 生成详细的权限报告
     * @param activity 当前Activity
     * @return 权限报告字符串
     */
    public static String generatePermissionReport(Activity activity) {
        StringBuilder report = new StringBuilder();
        report.append("=== P10DZ 权限状态报告 ===\n");
        report.append("应用包名: ").append(activity.getPackageName()).append("\n");
        report.append("应用类型: ").append(isSystemApp(activity) ? "系统应用" : "普通应用").append("\n");
        report.append("Android版本: ").append(android.os.Build.VERSION.RELEASE).append("\n");
        report.append("API级别: ").append(android.os.Build.VERSION.SDK_INT).append("\n\n");
        
        report.append("权限详情:\n");
        for (int i = 0; i < CRITICAL_PERMISSIONS.length; i++) {
            String permission = CRITICAL_PERMISSIONS[i];
            String name = PERMISSION_NAMES[i];
            boolean isGranted = activity.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED;
            
            report.append("- ").append(name).append(": ");
            report.append(isGranted ? "✓ 已授予" : "✗ 未授予").append("\n");
        }
        
        report.append("\n功能状态:\n");
        report.append("- 蓝牙功能: ").append(checkBluetoothPermissions(activity) ? "可用" : "受限").append("\n");
        report.append("- 位置功能: ").append(checkLocationPermissions(activity) ? "可用" : "受限").append("\n");
        
        report.append("\n=== 报告结束 ===");
        
        return report.toString();
    }
}
