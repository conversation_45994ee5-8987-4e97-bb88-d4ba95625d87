# P10DZ 运行时权限自动授予功能

## 功能说明

本功能实现了在 Android 10+ 系统上，系统应用（priv-app）自动授予运行时权限，无需用户手动点击"允许"按钮。

## 实现原理

在 Android 10 以后，即使是 signature|privileged 的系统应用，危险权限（如蓝牙、位置权限）仍然需要在运行时授予。本方案通过以下方式实现自动授权：

1. **PackageManager 反射调用**：通过反射调用系统隐藏API `grantRuntimePermission`
2. **AppOpsManager 权限设置**：通过AppOps系统设置权限状态
3. **Shell 命令授权**：通过 `pm grant` 命令强制授予权限

## 核心文件

### 1. PrivPermissionGranter.java
权限授予工具类，包含以下主要方法：
- `grantAll(Activity)`: 自动授予所有危险权限
- `grantBluetoothPermissions(Activity)`: 专门授予蓝牙权限
- `checkPermissionStatus(Activity)`: 检查权限授予状态

### 2. MainActivity.java 修改
在 `onCreate()` 方法中添加了权限自动授予调用：
```java
// 第一时间自动授予运行时权限（系统应用专用）
PrivPermissionGranter.grantAll(this);
```

## 支持的权限

自动授予以下运行时权限：
- `ACCESS_FINE_LOCATION` - 精确位置
- `ACCESS_COARSE_LOCATION` - 大致位置  
- `BLUETOOTH_SCAN` - 蓝牙扫描
- `BLUETOOTH_CONNECT` - 蓝牙连接
- `BLUETOOTH_ADVERTISE` - 蓝牙广播
- `RECORD_AUDIO` - 录音
- `WRITE_EXTERNAL_STORAGE` - 外部存储写入

## 使用条件

⚠️ **重要提醒**：此功能仅适用于以下环境：

1. **系统应用**：APK 必须安装在 `/system/priv-app/` 目录
2. **平台签名**：APK 必须使用 platform 密钥签名
3. **权限白名单**：在 `/system/etc/permissions/` 中配置权限白名单
4. **调试版本**：userdebug 或 eng 版本固件（user 版本可能无效）

## 验证方法

### 1. 查看日志
应用启动后，查看 logcat 日志：
```bash
adb logcat | grep -E "(PrivPermissionGranter|MainActivity)"
```

### 2. 检查权限状态
使用 adb 命令检查权限：
```bash
adb shell pm list permissions -d -g com.dbg.p10dz
```

### 3. 应用内提示
应用启动2秒后会显示Toast提示权限授予状态。

## 故障排除

### 1. 权限未授予
如果权限仍未授予，检查：
- 应用是否为系统应用（查看日志中的系统应用检查）
- 固件是否为 userdebug/eng 版本
- 权限白名单是否正确配置

### 2. 蓝牙功能异常
如果蓝牙功能仍有问题：
- 点击蓝牙按钮时会再次尝试授予蓝牙权限
- 查看日志中的蓝牙权限授予状态

### 3. 手动授权备选方案
如果自动授权失败，可以手动执行：
```bash
adb shell pm grant com.dbg.p10dz android.permission.BLUETOOTH_SCAN
adb shell pm grant com.dbg.p10dz android.permission.BLUETOOTH_CONNECT
adb shell pm grant com.dbg.p10dz android.permission.ACCESS_FINE_LOCATION
```

## 日志示例

成功授予权限的日志示例：
```
I/MainActivity: === P10DZ 应用启动，开始自动授予运行时权限 ===
I/PrivPermissionGranter: 开始自动授予运行时权限...
D/PrivPermissionGranter: 使用Android 11+方法签名
D/PrivPermissionGranter: 成功授予权限: android.permission.BLUETOOTH_SCAN
I/PrivPermissionGranter: 权限授予验证成功: android.permission.BLUETOOTH_SCAN
I/PrivPermissionGranter: 🎉 所有权限都已成功授予！
I/MainActivity: 权限状态提示: ✓ 所有运行时权限已自动授予
```

## 注意事项

1. 此功能仅在系统应用环境下有效
2. 不同 Android 版本的API可能有差异，工具类已适配多种方法签名
3. 权限授予是一次性的，重装应用后需要重新授予
4. 建议在应用发布前充分测试权限功能
