<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="14dp"
    android:gravity="center">

    <Button
        android:id="@+id/bluetooth_btn"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="16dp"
        android:backgroundTint="@color/red"
        android:text="喇叭测试"
        android:textSize="24sp" />

    <Button
        android:id="@+id/charging_btn"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginBottom="16dp"
        android:text="充电测试"
        android:backgroundTint="@color/red"
        android:textSize="24sp"/>

    <Button
        android:id="@+id/network_btn"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_marginBottom="16dp"
        android:text="网口测试"
        android:backgroundTint="@color/red"
        android:textSize="24sp"/>

    <Button
        android:id="@+id/scanner_btn"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:text="扫码头测试"
        android:backgroundTint="@color/red"
        android:textSize="24sp"/>

</LinearLayout>