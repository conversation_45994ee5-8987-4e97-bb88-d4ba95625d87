plugins {
    alias(libs.plugins.androidApplication)
}

android {
    namespace 'com.dbg.p10dz'
    compileSdk 34

    defaultConfig {
        applicationId "com.dbg.p10dz"
        minSdk 33
        targetSdk 34
        versionCode 8
        versionName "2.1.8"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            def versionName = variant.versionName
            outputFileName = "P10DZ_v${versionName}.apk"
        }
    }

    signingConfigs {
        platform {
            storeFile file('platform.jks')
            storePassword 'dbg20250623'
            keyAlias 'platform'
            keyPassword 'dbg20250623'
            v1SigningEnabled true
            v2SigningEnabled true
        }

        shared {
            storeFile file('shared.jks')
            storePassword 'dbg20250623'
            keyAlias 'shared'
            keyPassword 'dbg20250623'
            v1SigningEnabled true
            v2SigningEnabled true
        }

        media {
            storeFile file('media.jks')
            storePassword 'dbg20250623'
            keyAlias 'media'
            keyPassword 'dbg20250623'
            v1SigningEnabled true
            v2SigningEnabled true
        }

    }


}

dependencies {

    implementation fileTree(include: ['*.jar'], dir: 'libs')

    implementation 'com.google.zxing:core:3.4.1'
    implementation libs.appcompat
    implementation libs.material
    implementation libs.recyclerview
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core

    implementation 'com.squareup.okhttp3:okhttp:4.9.3'
}