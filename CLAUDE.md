# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

P10-DZ is an Android application for hardware testing and Bluetooth connectivity, specifically designed for POS device testing. The application can run in two modes:

1. **Normal App Mode**: Requires user permissions for Bluetooth operations
2. **System App Mode**: Uses system privileges for seamless Bluetooth operations without user prompts

## Build Commands

### Standard Build
```bash
# Clean build
./gradlew clean

# Build debug APK
./gradlew assembleDebug

# Build release APK
./gradlew assembleRelease
```

### System App Build (with system signing)
```bash
# Build and sign with system keys
./build_system_app.sh

# Deploy system app
./deploy_system_app.sh
```

### Testing
```bash
# Run unit tests
./gradlew test

# Run instrumented tests
./gradlew connectedAndroidTest
```

## Key Architecture Components

### Main Activities
- **MainActivity**: Core testing interface with hardware validation (charging, network, scanner, LED)
- **BtActivity**: Bluetooth connectivity with dual-mode support (normal/system app)
- **AgingTestActivity**: Hardware stress testing
- **NetworkStressTestActivity**: Network performance testing

### Bluetooth Management
- **BtManager**: Handles normal app Bluetooth operations with permission requests
- **SystemBtManager**: Handles system app Bluetooth operations without user prompts

### Utility Classes
- **SPUtils**: SharedPreferences helper
- **SerialPortUtils**: Serial communication for hardware testing

## Application Configuration

### System App Requirements
- Uses `android.uid.system` sharedUserId
- Requires system signing for privileged permissions
- Key permissions: `BLUETOOTH_PRIVILEGED`, `BLUETOOTH_CONNECT`, `BLUETOOTH_SCAN`

### Build Configuration
- Target SDK: 34
- Min SDK: 33
- Java 8 compatibility
- Uses Aliyun Maven mirrors for Chinese development environment

## Key Features

### Bluetooth Auto-connection
- Target device: "BlueCash-05 cradle"
- Auto-detection, pairing, and music playback
- Automatic cleanup (unpair + disable) after completion

### Hardware Testing
- **Charging Detection**: Monitors power connection state
- **Network Testing**: Ethernet connectivity with gateway ping tests
- **LED Validation**: Visual inspection of network port indicators
- **Scanner Testing**: QR code validation with expected codes

### System App Mode
When running as a system app:
- No permission prompts for Bluetooth operations
- Automatic Bluetooth enable/disable
- Silent pairing without user confirmation
- MAC address retrieval and QR code generation

## Development Notes

### Code Style
- Uses Chinese comments and logs
- Mixed Chinese/English variable names
- Legacy reflection usage for system operations

### Testing Hardware
- Designed for specific POS hardware with Ethernet and Bluetooth capabilities
- Requires physical device testing (emulator not suitable)

### Build Output
- APK naming: `P10DZ_v{versionName}.apk`
- Version tracking in build.gradle

## Troubleshooting

### Common Issues
1. **Bluetooth Permissions**: Ensure proper system signing for privileged operations
2. **Network Testing**: Verify Ethernet interface (eth0) is available
3. **MAC Address**: Check hardware connectivity for MAC retrieval
4. **System App Mode**: Requires installation in /system/priv-app/ with proper permissions

### Log Debugging
- Look for tags: "lyc---", "coco", "hxx", "AgingTest"
- Use `adb logcat` for real-time debugging
- System app operations require root access for full log visibility