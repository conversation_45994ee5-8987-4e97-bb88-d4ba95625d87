1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.dbg.p10dz"
4    android:versionCode="8"
5    android:versionName="2.1.8" >
6
7    <uses-sdk
8        android:minSdkVersion="33"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
11-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:5:5-78
11-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:5:22-76
12    <!-- 系统应用蓝牙权限配置 -->
13    <uses-permission android:name="android.permission.BLUETOOTH" />
13-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:7:5-68
13-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:7:22-65
14    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
14-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:8:5-74
14-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:8:22-71
15    <uses-permission
15-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:9:5-10:58
16        android:name="android.permission.BLUETOOTH_CONNECT"
16-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:9:22-73
17        android:usesPermissionFlags="neverForLocation" />
17-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:10:9-55
18    <uses-permission
18-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:11:5-12:58
19        android:name="android.permission.BLUETOOTH_SCAN"
19-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:11:22-70
20        android:usesPermissionFlags="neverForLocation" />
20-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:12:9-55
21    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
21-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:13:5-78
21-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:13:22-75
22
23    <!-- 系统级位置权限 -->
24    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
24-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:16:5-79
24-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:16:22-76
25    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
25-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:17:5-81
25-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:17:22-78
26
27    <!-- 系统级权限，无需用户授权 -->
28    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
28-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:20:5-21:47
28-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:20:22-76
29
30    <!-- 其他权限 -->
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:5:5-78
31-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:5:22-76
32    <uses-permission android:name="android.permission.INTERNET" />
32-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:25:5-66
32-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:25:22-64
33    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
33-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:26:5-75
33-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:26:22-73
34    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
34-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:27:5-81
34-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:27:22-78
35    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
35-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:28:5-80
35-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:28:22-77
36    <uses-permission
36-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:29:5-30:47
37        android:name="android.permission.WRITE_SETTINGS"
37-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:29:22-70
38        android:required="true" />
38-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:29:71-94
39    <uses-permission android:name="android.permission.USB_PERMISSION" />
39-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:31:5-73
39-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:31:22-70
40    <uses-permission android:name="android.permission.USB_DEVICE_ADMIN" />
40-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:32:5-75
40-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:32:22-72
41    <uses-permission android:name="android.permission.RECORD_AUDIO" />
41-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:33:5-71
41-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:33:22-68
42
43    <permission
43-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
44        android:name="com.dbg.p10dz.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.dbg.p10dz.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
48
49    <application
49-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:34:5-80:19
50        android:allowBackup="true"
50-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:35:9-35
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ef6750e3aeef24596ce8cfe06d8569d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
52        android:dataExtractionRules="@xml/data_extraction_rules"
52-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:36:9-65
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:fullBackupContent="@xml/backup_rules"
55-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:37:9-54
56        android:icon="@mipmap/ic_launcher"
56-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:38:9-43
57        android:label="@string/app_name"
57-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:39:9-41
58        android:networkSecurityConfig="@xml/network_security_config"
58-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:43:9-69
59        android:roundIcon="@mipmap/ic_launcher_round"
59-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:40:9-54
60        android:sharedUserId="android.uid.system"
60-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:45:9-50
61        android:supportsRtl="true"
61-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:41:9-35
62        android:testOnly="true"
63        android:theme="@style/Theme.P10DZ"
63-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:44:9-43
64        android:usesCleartextTraffic="true" >
64-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:42:9-44
65        <activity
65-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:47:9-59:20
66            android:name="com.dbg.p10dz.MainActivity"
66-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:49:13-41
67            android:exported="true"
67-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:50:13-36
68            android:launchMode="singleInstance" >
68-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:48:13-48
69            <intent-filter>
69-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:51:13-55:29
70                <action android:name="android.intent.action.MAIN" />
70-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:52:17-69
70-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:52:25-66
71
72                <category android:name="android.intent.category.LAUNCHER" />
72-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:53:17-77
72-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:53:27-74
73
74                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
74-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:54:17-90
74-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:54:25-87
75            </intent-filter>
76
77            <meta-data
77-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:56:13-58:57
78                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
78-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:57:17-79
79                android:resource="@xml/device_filter" />
79-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:58:17-54
80        </activity>
81        <activity
81-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:61:9-66:47
82            android:name="com.dbg.p10dz.AgingTestActivity"
82-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:62:13-46
83            android:configChanges="orientation|screenSize|keyboardHidden"
83-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:65:13-74
84            android:exported="false"
84-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:64:13-37
85            android:label="老化测试"
85-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:63:13-33
86            android:launchMode="singleTask" />
86-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:66:13-44
87        <activity
87-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:68:9-73:76
88            android:name="com.dbg.p10dz.NetworkStressTestActivity"
88-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:69:13-54
89            android:configChanges="orientation|screenSize|keyboardHidden"
89-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:73:13-74
90            android:exported="false"
90-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:70:13-37
91            android:launchMode="singleTask"
91-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:72:13-44
92            android:taskAffinity="" />
92-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:71:13-36
93        <activity
93-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:75:9-79:76
94            android:name="com.dbg.p10dz.BtActivity"
94-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:75:19-45
95            android:configChanges="orientation|screenSize|keyboardHidden"
95-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:79:13-74
96            android:exported="false"
96-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:76:13-37
97            android:launchMode="singleTask"
97-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:78:13-44
98            android:taskAffinity="" />
98-->C:\Users\<USER>\Desktop\error\p10dz\app\src\main\AndroidManifest.xml:77:13-36
99
100        <provider
100-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
101            android:name="androidx.startup.InitializationProvider"
101-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
102            android:authorities="com.dbg.p10dz.androidx-startup"
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
103            android:exported="false" >
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
104            <meta-data
104-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.emoji2.text.EmojiCompatInitializer"
105-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
106                android:value="androidx.startup" />
106-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c40363add2c179a5c34bfeeccc546b2a\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
108-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
109                android:value="androidx.startup" />
109-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\e21c9972547f416b82724a411268ce39\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
112                android:value="androidx.startup" />
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
113        </provider>
114
115        <receiver
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
116            android:name="androidx.profileinstaller.ProfileInstallReceiver"
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
117            android:directBootAware="false"
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
118            android:enabled="true"
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
119            android:exported="true"
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
120            android:permission="android.permission.DUMP" >
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
122                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
123            </intent-filter>
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
125                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
125-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
128                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
128-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
131                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
131-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e567a117d9643f6df3fb9156af9b05d\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
132            </intent-filter>
133        </receiver>
134    </application>
135
136</manifest>
