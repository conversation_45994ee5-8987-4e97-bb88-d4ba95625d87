<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="6dp">

    <TextView
        android:id="@+id/tv_aging_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="vertical">

        <ProgressBar
            android:id="@+id/progress_download"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_marginTop="4dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_download_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="已完成下载次数: 0"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_download_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="下载进度: 0%"
                android:textSize="14sp" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_system_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="14sp"
            android:textColor="#666666"
            android:lineSpacingExtra="2dp"/>
    </LinearLayout>

    <ScrollView
        android:id="@+id/tv_aging_log_scrollview"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_weight="1">

        <TextView
            android:id="@+id/tv_aging_log"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp" />
    </ScrollView>

    <Button
        android:id="@+id/networkStressTestBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="网口压力测试"/>

</LinearLayout> 