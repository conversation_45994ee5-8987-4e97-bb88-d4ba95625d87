<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Switch主题样式 -->
    <style name="SwitchTheme" parent="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge">
        <item name="colorControlActivated">#4CAF50</item>
    </style>


    <style name="AgingButtonStyle" parent="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge">
        <item name="backgroundTint">@color/button_color</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="CustomDialogTitle" parent="TextAppearance.AppCompat">
        <item name="android:textColor">#FF0000</item> <!-- 红色 -->
        <item name="android:textSize">20sp</item> <!-- 字体大小 -->
    </style>

    <style name="DialogNoAnimation">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>
</resources>