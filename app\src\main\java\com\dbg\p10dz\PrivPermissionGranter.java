package com.dbg.p10dz;

import android.Manifest;
import android.app.Activity;
import android.app.AppOpsManager;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Process;
import android.os.UserHandle;
import android.util.Log;

import java.lang.reflect.Method;

/**
 * 一次性把 priv-app 声明的所有危险权限全部 grant 掉。
 * 只能在 userdebug / eng 或 root 设备上使用！
 * 
 * 原理说明：
 * 在 Android 10 以后，只有 signature|privileged 且被写进白名单的系统应用，才能
 * - 在安装时就授予「危险权限」
 * - 在运行时 无需弹窗 使用这些权限
 * 
 * 但危险权限仍然被当作运行时权限处理，所以第一次启动时系统还是会弹窗。
 * 这个工具类通过反射调用系统API来自动授予权限，实现无感知授权。
 */
public class PrivPermissionGranter {

    private static final String TAG = "PrivPermissionGranter";

    /**
     * 需要自动授予的危险权限列表
     */
    private static final String[] DANGEROUS_PERMISSIONS = {
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_ADVERTISE,
            Manifest.permission.RECORD_AUDIO,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
    };

    /**
     * 自动授予所有危险权限
     * @param activity 当前Activity
     */
    public static void grantAll(Activity activity) {
        if (!isSystemApp(activity)) {
            Log.w(TAG, "当前应用不是系统应用，无法自动授予权限");
            return;
        }

        Log.i(TAG, "开始自动授予运行时权限...");

        try {
            // 方法1：通过PackageManager的隐藏API授予权限
            if (grantPermissionsByPackageManager(activity)) {
                Log.i(TAG, "通过PackageManager成功授予权限");
            }

            // 方法2：通过AppOpsManager授予权限
            if (grantPermissionsByAppOps(activity)) {
                Log.i(TAG, "通过AppOpsManager成功授予权限");
            }

            // 方法3：通过Shell命令授予权限（需要root权限）
            if (grantPermissionsByShell(activity)) {
                Log.i(TAG, "通过Shell命令成功授予权限");
            }

            // 延迟检查权限状态
            activity.runOnUiThread(() -> {
                android.os.Handler handler = new android.os.Handler();
                handler.postDelayed(() -> {
                    Log.i(TAG, "延迟检查权限状态...");
                    checkPermissionStatus(activity);
                }, 1000); // 1秒后检查
            });

        } catch (Throwable t) {
            Log.e(TAG, "授予权限时发生异常", t);
        }
    }

    /**
     * 通过PackageManager的隐藏API授予权限
     */
    private static boolean grantPermissionsByPackageManager(Activity activity) {
        try {
            PackageManager pm = activity.getPackageManager();
            String packageName = activity.getPackageName();

            // 获取当前用户ID
            int userId = getCurrentUserId();

            // 尝试多种方法签名
            Method grantMethod = null;
            Object[] args = null;

            // 方法1：Android 11+ 的新方法签名
            try {
                grantMethod = pm.getClass().getDeclaredMethod("grantRuntimePermission",
                        String.class, String.class, int.class);
                args = new Object[]{packageName, null, userId};
                Log.d(TAG, "使用Android 11+方法签名");
            } catch (NoSuchMethodException e) {
                // 方法2：Android 10 的方法签名
                try {
                    grantMethod = pm.getClass().getDeclaredMethod("grantRuntimePermission",
                            String.class, String.class, UserHandle.class);
                    args = new Object[]{packageName, null, Process.myUserHandle()};
                    Log.d(TAG, "使用Android 10方法签名");
                } catch (NoSuchMethodException e2) {
                    // 方法3：尝试更简单的方法签名
                    try {
                        grantMethod = pm.getClass().getDeclaredMethod("grantRuntimePermission",
                                String.class, String.class);
                        args = new Object[]{packageName, null};
                        Log.d(TAG, "使用简化方法签名");
                    } catch (NoSuchMethodException e3) {
                        Log.e(TAG, "找不到合适的grantRuntimePermission方法");
                        return false;
                    }
                }
            }

            if (grantMethod != null) {
                grantMethod.setAccessible(true);

                int grantedCount = 0;
                for (String permission : DANGEROUS_PERMISSIONS) {
                    if (activity.checkSelfPermission(permission) == PackageManager.PERMISSION_DENIED) {
                        try {
                            args[1] = permission; // 设置权限名称
                            grantMethod.invoke(pm, args);
                            Log.d(TAG, "成功授予权限: " + permission);
                            grantedCount++;

                            // 立即验证权限是否真的被授予
                            if (activity.checkSelfPermission(permission) == PackageManager.PERMISSION_GRANTED) {
                                Log.i(TAG, "权限授予验证成功: " + permission);
                            } else {
                                Log.w(TAG, "权限授予验证失败: " + permission);
                            }

                        } catch (Exception e) {
                            Log.w(TAG, "授予权限失败: " + permission, e);
                        }
                    } else {
                        Log.d(TAG, "权限已授予: " + permission);
                    }
                }

                Log.i(TAG, "通过PackageManager授予了 " + grantedCount + " 个权限");
                return grantedCount > 0;
            }

        } catch (Throwable t) {
            Log.e(TAG, "PackageManager授权方法失败", t);
        }

        return false;
    }

    /**
     * 通过AppOpsManager授予权限
     */
    private static boolean grantPermissionsByAppOps(Activity activity) {
        try {
            AppOpsManager appOpsManager = (AppOpsManager) activity.getSystemService(Context.APP_OPS_SERVICE);
            if (appOpsManager == null) {
                return false;
            }
            
            String packageName = activity.getPackageName();
            int uid = activity.getApplicationInfo().uid;
            
            // 获取setMode方法
            Method setModeMethod = appOpsManager.getClass().getDeclaredMethod("setMode",
                    int.class, int.class, String.class, int.class);
            setModeMethod.setAccessible(true);
            
            int grantedCount = 0;
            for (String permission : DANGEROUS_PERMISSIONS) {
                try {
                    // 将权限转换为对应的AppOps操作
                    String opName = getAppOpFromPermission(permission);
                    if (opName != null) {
                        // 获取操作码
                        int opCode = getOpCode(opName);
                        if (opCode >= 0) {
                            // 设置为允许模式
                            setModeMethod.invoke(appOpsManager, opCode, uid, packageName, AppOpsManager.MODE_ALLOWED);
                            Log.d(TAG, "通过AppOps授予权限: " + permission);
                            grantedCount++;
                        }
                    }
                } catch (Exception e) {
                    Log.w(TAG, "AppOps授予权限失败: " + permission, e);
                }
            }
            
            Log.i(TAG, "通过AppOpsManager授予了 " + grantedCount + " 个权限");
            return grantedCount > 0;
            
        } catch (Throwable t) {
            Log.e(TAG, "AppOpsManager授权方法失败", t);
        }
        
        return false;
    }

    /**
     * 通过Shell命令授予权限（需要root权限或系统权限）
     */
    private static boolean grantPermissionsByShell(Activity activity) {
        try {
            String packageName = activity.getPackageName();
            int grantedCount = 0;

            for (String permission : DANGEROUS_PERMISSIONS) {
                if (activity.checkSelfPermission(permission) == PackageManager.PERMISSION_DENIED) {
                    try {
                        // 构造pm grant命令
                        String command = "pm grant " + packageName + " " + permission;

                        // 尝试执行命令
                        java.lang.Process process = Runtime.getRuntime().exec(new String[]{"su", "-c", command});
                        int exitCode = process.waitFor();

                        if (exitCode == 0) {
                            Log.d(TAG, "Shell命令成功授予权限: " + permission);
                            grantedCount++;
                        } else {
                            // 如果su失败，尝试直接执行（系统应用可能有权限）
                            process = Runtime.getRuntime().exec(command);
                            exitCode = process.waitFor();
                            if (exitCode == 0) {
                                Log.d(TAG, "直接Shell命令成功授予权限: " + permission);
                                grantedCount++;
                            } else {
                                Log.w(TAG, "Shell命令授予权限失败: " + permission + ", exitCode: " + exitCode);
                            }
                        }
                    } catch (Exception e) {
                        Log.w(TAG, "Shell命令授予权限异常: " + permission, e);
                    }
                }
            }

            Log.i(TAG, "通过Shell命令授予了 " + grantedCount + " 个权限");
            return grantedCount > 0;

        } catch (Throwable t) {
            Log.e(TAG, "Shell命令授权方法失败", t);
        }

        return false;
    }

    /**
     * 检查是否为系统应用
     */
    private static boolean isSystemApp(Context ctx) {
        int flags = ctx.getApplicationInfo().flags;
        boolean isSystem = (flags & android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0;

        // FLAG_PRIVILEGED 在某些版本中可能不可用，使用反射获取
        boolean isPrivileged = false;
        try {
            java.lang.reflect.Field privilegedField = android.content.pm.ApplicationInfo.class.getField("FLAG_PRIVILEGED");
            int privilegedFlag = privilegedField.getInt(null);
            isPrivileged = (flags & privilegedFlag) != 0;
        } catch (Exception e) {
            Log.d(TAG, "无法获取FLAG_PRIVILEGED，可能是较老的Android版本");
        }

        Log.d(TAG, "应用标志检查 - isSystem: " + isSystem + ", isPrivileged: " + isPrivileged);
        return isSystem || isPrivileged;
    }

    /**
     * 获取当前用户ID
     */
    private static int getCurrentUserId() {
        try {
            Method getUserIdMethod = UserHandle.class.getDeclaredMethod("myUserId");
            getUserIdMethod.setAccessible(true);
            return (Integer) getUserIdMethod.invoke(null);
        } catch (Exception e) {
            Log.w(TAG, "获取用户ID失败，使用默认值0", e);
            return 0;
        }
    }

    /**
     * 将权限转换为对应的AppOps操作名称
     */
    private static String getAppOpFromPermission(String permission) {
        switch (permission) {
            case Manifest.permission.ACCESS_FINE_LOCATION:
                return "android:fine_location";
            case Manifest.permission.ACCESS_COARSE_LOCATION:
                return "android:coarse_location";
            case Manifest.permission.RECORD_AUDIO:
                return "android:record_audio";
            case Manifest.permission.WRITE_EXTERNAL_STORAGE:
                return "android:write_external_storage";
            default:
                return null;
        }
    }

    /**
     * 获取AppOps操作码
     */
    private static int getOpCode(String opName) {
        try {
            // 通过反射获取AppOpsManager中的操作码常量
            String constName = opName.replace("android:", "OP_").toUpperCase();
            java.lang.reflect.Field field = AppOpsManager.class.getDeclaredField(constName);
            field.setAccessible(true);
            return field.getInt(null);
        } catch (Exception e) {
            Log.w(TAG, "获取操作码失败: " + opName, e);
            return -1;
        }
    }

    /**
     * 专门处理蓝牙权限的授予
     */
    public static void grantBluetoothPermissions(Activity activity) {
        if (!isSystemApp(activity)) {
            Log.w(TAG, "当前应用不是系统应用，无法自动授予蓝牙权限");
            return;
        }

        Log.i(TAG, "开始专门授予蓝牙权限...");

        String[] bluetoothPermissions = {
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_ADVERTISE
        };

        try {
            PackageManager pm = activity.getPackageManager();
            String packageName = activity.getPackageName();

            // 尝试通过反射直接设置权限状态
            for (String permission : bluetoothPermissions) {
                if (activity.checkSelfPermission(permission) == PackageManager.PERMISSION_DENIED) {
                    try {
                        // 方法1：通过PackageManager直接授予
                        Method grantMethod = pm.getClass().getDeclaredMethod("grantRuntimePermission",
                                String.class, String.class, int.class);
                        grantMethod.setAccessible(true);
                        grantMethod.invoke(pm, packageName, permission, getCurrentUserId());

                        Log.i(TAG, "蓝牙权限授予成功: " + permission);
                    } catch (Exception e) {
                        Log.w(TAG, "蓝牙权限授予失败: " + permission, e);

                        // 方法2：通过Shell命令强制授予
                        try {
                            String command = "pm grant " + packageName + " " + permission;
                            java.lang.Process process = Runtime.getRuntime().exec(command);
                            int exitCode = process.waitFor();
                            if (exitCode == 0) {
                                Log.i(TAG, "通过Shell命令成功授予蓝牙权限: " + permission);
                            }
                        } catch (Exception e2) {
                            Log.w(TAG, "Shell命令授予蓝牙权限也失败: " + permission, e2);
                        }
                    }
                }
            }
        } catch (Throwable t) {
            Log.e(TAG, "蓝牙权限授予过程发生异常", t);
        }
    }

    /**
     * 检查权限授予状态
     */
    public static void checkPermissionStatus(Activity activity) {
        Log.i(TAG, "=== 权限状态检查 ===");
        boolean allGranted = true;
        for (String permission : DANGEROUS_PERMISSIONS) {
            int status = activity.checkSelfPermission(permission);
            String statusText = (status == PackageManager.PERMISSION_GRANTED) ? "✓ 已授予" : "✗ 未授予";
            Log.i(TAG, permission + ": " + statusText);
            if (status != PackageManager.PERMISSION_GRANTED) {
                allGranted = false;
            }
        }

        if (allGranted) {
            Log.i(TAG, "🎉 所有权限都已成功授予！");
        } else {
            Log.w(TAG, "⚠️ 仍有权限未授予，可能需要手动处理");
        }
        Log.i(TAG, "=== 检查完成 ===");
    }
}
