package com.dbg.p10dz;

import android.app.Dialog;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.InputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import android.os.BatteryManager;
import android.content.Intent;
import android.content.IntentFilter;
import android.app.ActivityManager;
import android.content.Context;
import java.io.BufferedReader;
import java.io.FileReader;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.view.WindowManager;
import android.provider.Settings;
import android.net.Uri;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import java.util.HashMap;
import android.view.View;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import android.widget.Button;

// 底座VID PID
// Bus 001 Device 001: ID 1d6b:0002 (MTK)
// Bus 001 Device 003: ID 34eb:1502 (RTL8152)
// Bus 001 Device 127: ID 1a40:0101
// Bus 001 Device 002: ID 0bda:8152

public class AgingTestActivity extends AppCompatActivity {
    private TextView tvAgingStatus;
    private TextView tvAgingLog;
    private Handler handler;
    private boolean isTestRunning = false;
    private long startTime = 0;
    private SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
    private static final String DOWNLOAD_URL_HIGH = "https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb"; // Debian 安装镜像
    private static final String DOWNLOAD_URL_LOW = "https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb"; // 替换为小文件的URL
    private static final String DOWNLOAD_DIR = "aging_test";
    private int downloadCount = 0;
    private Thread downloadThread;
    private boolean isDownloading = false;
    private ProgressBar progressDownload;
    private TextView tvDownloadProgress;
    private TextView tvDownloadCount;
    private ScrollView mScrollViewAgingLog;
    private static final int MSG_UPDATE_STATUS = 1;
    private static final int MSG_APPEND_LOG = 2;
    private static final int MSG_UPDATE_PROGRESS = 3;
    private static final int MSG_UPDATE_DOWNLOAD_COUNT = 4;
    private static final String LOG_DIR = "aging_logs";
    private File logFile;
    private BatteryManager batteryManager;
    private ActivityManager activityManager;
    private static final long DEFAULT_TEST_DURATION = 8 * 60 * 60; // 8小时，单位：秒
    private long testDuration = DEFAULT_TEST_DURATION;
    private boolean isHighLevel = true; // 默认高强度
    private MediaPlayer mediaPlayer;
    private AudioManager audioManager;
    private static final int VOLUME_HIGH = 15;  // 高强度音量
    private static final int VOLUME_LOW = 7;    // 低强度音量
    private static final float BRIGHTNESS_HIGH = 1.0f;  // 高强度亮度 100%
    private static final float BRIGHTNESS_LOW = 0.1f;   // 低强度亮度 50%
    private UsbManager usbManager;

    private boolean enableFileLogging = false; // 控制是否写入日志文件的开关

    private TextView tvSystemInfo;

    private boolean enableDownloadTest = false; // 控制是否进行下载测试的开关

    private static final long DOWNLOAD_INTERVAL = 5 * 60 * 1000; // 5分钟的毫秒数
    private long lastDownloadTime = 0;

    private static final int SYSTEM_INFO_INTERVAL = 5000; // 5秒收集一次系统信息
    private static final int BUFFER_SIZE = 16384; // 16KB 缓冲区
    private static final int LOG_BUFFER_SIZE = 1024; // 日志缓冲区大小
    private static final int PROGRESS_UPDATE_INTERVAL = 500; // 500ms更新一次进度
    
    private ExecutorService executorService; // 线程池
    private StringBuilder logBuffer; // 日志缓冲区
    private long lastSystemInfoTime = 0;

    private Button networkStressTestBtn;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化日志缓冲区
        logBuffer = new StringBuilder(LOG_BUFFER_SIZE);
        
        setContentView(R.layout.activity_aging_test);

        // 从Intent中获取测试时长（分钟）
        long durationMinutes = getIntent().getLongExtra("duration", DEFAULT_TEST_DURATION / 60);
        String level = getIntent().getStringExtra("level");
        isHighLevel = "high".equals(level);

        enableFileLogging = true;
        testDuration = durationMinutes * 60; // 将分钟转换为秒
        
        // 初始化线程池
        executorService = Executors.newFixedThreadPool(3);

        // 先初始化 handler
        initHandler();
        
        // 初始化所有组件
        initViews();
        initComponents();

        // 设置标题
        setTitle("老化测试 - " + (isHighLevel ? "高强度" : "低强度"));

        // 添加日志记录实际设置的时长
        appendLog("设置测试时长: " + durationMinutes + "分钟");

        // 恢复保存的状态
        if (savedInstanceState != null) {
            isTestRunning = savedInstanceState.getBoolean("isTestRunning", false);
            isDownloading = savedInstanceState.getBoolean("isDownloading", false);
            downloadCount = savedInstanceState.getInt("downloadCount", 0);
        }

        // 启动测试
        startAgingTest();
    }

    private void initViews() {
        tvAgingStatus = findViewById(R.id.tv_aging_status);
        tvAgingLog = findViewById(R.id.tv_aging_log);
        progressDownload = findViewById(R.id.progress_download);
        tvDownloadProgress = findViewById(R.id.tv_download_progress);
        tvDownloadCount = findViewById(R.id.tv_download_count);
        mScrollViewAgingLog = findViewById(R.id.tv_aging_log_scrollview);
        tvSystemInfo = findViewById(R.id.tv_system_info);
        
        // 初始化网口压力测试按钮
        networkStressTestBtn = findViewById(R.id.networkStressTestBtn);
        if (networkStressTestBtn != null) {
            networkStressTestBtn.setOnClickListener(v -> startNetworkStressTest());
        }

        if(tvSystemInfo != null){
            if(enableFileLogging == false){
                tvSystemInfo.setVisibility(View.VISIBLE); 
            }else{
                tvSystemInfo.setVisibility(View.GONE); 
            }
        }

        // 设置进度条最大值
        if(progressDownload != null){
            progressDownload.setMax(100);
        }

        if (handler != null) {
            updateStatus("未开始测试");
        }

        // 根据下载测试开关控制UI显示
        if (progressDownload != null) {
            progressDownload.setVisibility(enableDownloadTest ? View.VISIBLE : View.GONE);
        }
        if (tvDownloadProgress != null) {
            tvDownloadProgress.setVisibility(enableDownloadTest ? View.VISIBLE : View.GONE);
        }
        if (tvDownloadCount != null) {
            tvDownloadCount.setVisibility(enableDownloadTest ? View.VISIBLE : View.GONE);
        }
    }

    private void initHandler() {
        handler = new Handler(Looper.getMainLooper()) {
            @Override
            public void handleMessage(Message msg) {
                if (!isFinishing()) {  // 添加检查
                    switch (msg.what) {
                        case MSG_UPDATE_STATUS:
                            String statusText = (String) msg.obj;
                            if (tvAgingStatus != null) {
                                if (statusText.contains("<")) {
                                    tvAgingStatus.setText(android.text.Html.fromHtml(statusText));
                                } else {
                                    tvAgingStatus.setText(statusText);
                                }
                            }
                            break;
                        case MSG_APPEND_LOG:
                            tvAgingLog.append((String) msg.obj);
                            mScrollViewAgingLog.post(() -> 
                                mScrollViewAgingLog.fullScroll(ScrollView.FOCUS_DOWN));
                            break;
                        case MSG_UPDATE_PROGRESS:
                            int progress = msg.arg1;
                            if(progressDownload != null){
                                progressDownload.setProgress(progress);
                            }
                            tvDownloadProgress.setText(String.format("下载进度: %d%%", progress));
                            break;
                        case MSG_UPDATE_DOWNLOAD_COUNT:
                            tvDownloadCount.setText(String.format("已完成下载次数: %d", msg.arg1));
                            break;
                    }
                }
            }
        };
    }

    private void initComponents() {
        // 初始化系统服务
        batteryManager = (BatteryManager) getSystemService(Context.BATTERY_SERVICE);
        activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        mediaPlayer = MediaPlayer.create(this, R.raw.test_music);
        mediaPlayer.setLooping(true);
        usbManager = (UsbManager) getSystemService(Context.USB_SERVICE);
        initLogFile();
    }

    private void setBrightness(float f) {
        try {
            // 先检查权限
            if (!Settings.System.canWrite(this)) {
                appendLog("无修改系统设置权限，使用备选方案");
                WindowManager.LayoutParams lp = getWindow().getAttributes();
                lp.screenBrightness = f;
                getWindow().setAttributes(lp);
                return;
            }

            // 使用系统命令设置亮度
            Process process = Runtime.getRuntime().exec(new String[]{"su", "-c", "echo 255 > /sys/class/leds/lcd-backlight/brightness"});
            process.waitFor();

            // 同时也设置窗口亮度属性
            WindowManager.LayoutParams lp = getWindow().getAttributes();
            lp.screenBrightness = f;
            getWindow().setAttributes(lp);
            
            appendLog("设置屏幕亮度成功");
        } catch (Exception e) {
            appendLog("设置屏幕亮度失败: " + e.getMessage());
            
            // 如果系统命令失败，使用备选方案
            try {
                WindowManager.LayoutParams lp = getWindow().getAttributes();
                lp.screenBrightness = f;
                getWindow().setAttributes(lp);
            } catch (Exception e2) {
                appendLog("备选亮度设置方式也失败: " + e2.getMessage());
            }
        }
    }

    private void startAgingTest() {
        isTestRunning = true;
        isDownloading = enableDownloadTest; // 根据开关决定是否开始下载
        startTime = System.currentTimeMillis();
        downloadCount = 0;

        // 重置进度条和计数
        if(progressDownload != null && enableDownloadTest){
            progressDownload.setProgress(0);
        }

        if (enableDownloadTest) {
            updateDownloadCount(0);
        }

        appendLog("开始老化测试");
        appendLog("日志文件路径: " + logFile.getAbsolutePath());
        updateStatus("测试进行中...");

        startStatusUpdate();
        
        // 根据开关决定是否启动下载测试
        if (enableDownloadTest) {
            lastDownloadTime = System.currentTimeMillis() - DOWNLOAD_INTERVAL; // 将最后下载时间设置为当前时间减去间隔，这样可以立即触发第一次下载
            startDownloadTest();
            appendLog("启动下载测试，将立即开始第一次下载");
        } else {
            appendLog("下载测试已禁用");
        }

        // 添加开始时间日志
        String startTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                .format(new Date(startTime));
        appendLog("开始老化测试时间: " + startTimeStr);

        // 设置音量并开始播放音乐
        int maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_MUSIC);
        int targetVolume = isHighLevel ? VOLUME_HIGH : VOLUME_LOW;
        int volume = Math.min(targetVolume, maxVolume);
        audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, volume, 0);

        if (mediaPlayer != null) {
            mediaPlayer.start();
            appendLog("开始播放音乐，音量级别: " + volume);
        }

        // 设置屏幕亮度
        try {
            android.provider.Settings.System.putInt(
                getContentResolver(),
                android.provider.Settings.System.SCREEN_BRIGHTNESS_MODE,
                android.provider.Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL
            );
            
            float brightness = isHighLevel ? BRIGHTNESS_HIGH : BRIGHTNESS_LOW;
            setBrightness(brightness);
            appendLog("设置屏幕亮度: " + (brightness * 100) + "%");
        } catch (Exception e) {
            appendLog("设置屏幕亮度失败: " + e.getMessage());
        }
    }

    private void stopAgingTest() {
        Log.d("coco","hxx----stopAgingTest----isFinishing(): " + isFinishing());
        // 添加检查，避免配置变化时触发停止
        if (isFinishing() || isChangingConfigurations()) {  
            return;
        }

        isTestRunning = false;
        isDownloading = false;

        if (downloadThread != null) {
            downloadThread.interrupt();
            downloadThread = null;
        }

        appendLog("停止老化测试");
        if (enableDownloadTest) {
            appendLog("本次测试共完成下载 " + downloadCount + " 次");
        }
        updateStatus("测试已停止");

        // 重置进度条
        if (enableDownloadTest) {
            progressDownload.setProgress(0);
            tvDownloadProgress.setText("下载进度: 0%");
        }

        handler.removeCallbacksAndMessages(null);

        // 计算并记录总测试时长
        long endTime = System.currentTimeMillis();
        long totalDuration = (endTime - startTime) / 1000;
        String durationStr = String.format(Locale.getDefault(),
                "%02d:%02d:%02d",
                totalDuration / 3600, 
                (totalDuration % 3600) / 60, 
                totalDuration % 60);
                
        appendLog(String.format("结束老化测试，总时长: %s，共完成下载 %d 次", durationStr, downloadCount));

        // 停止音乐播放
        if (mediaPlayer != null && mediaPlayer.isPlaying()) {
            mediaPlayer.stop();
            appendLog("停止音乐播放");
        }

        // 恢复系统亮度模式
        try {
            android.provider.Settings.System.putInt(
                getContentResolver(),
                android.provider.Settings.System.SCREEN_BRIGHTNESS_MODE,
                android.provider.Settings.System.SCREEN_BRIGHTNESS_MODE_AUTOMATIC
            );
            appendLog("已恢复自动亮度模式");
        } catch (Exception e) {
            appendLog("恢复亮度模式失败: " + e.getMessage());
        }

        // 在显示对话框之前检查活动状态
        if (!isFinishing()) {
            showTestResultDialog();
        }
    }

    private void showTestResultDialog() {
        if (isFinishing()) {  // 再次检查活动状态
            return;
        }

        runOnUiThread(() -> {
            if (isFinishing()) {  // UI线程中再次检查
                return;
            }

            Dialog dialog = new Dialog(this);
            dialog.setContentView(R.layout.dialog_aging_result);
            dialog.setCancelable(false);
            
            TextView tvResult = dialog.findViewById(R.id.tv_aging_result);
            TextView tvSummary = dialog.findViewById(R.id.tv_aging_summary);
            Button btnConfirm = dialog.findViewById(R.id.btn_confirm);

            // 计算实际运行时间（秒）
            long actualDuration = (System.currentTimeMillis() - startTime) / 1000;

            // 判断测试结果：实际运行时间大于等于设定时间即为成功
            boolean isSuccess = actualDuration >= testDuration;

            // 设置结果文本和颜色
            tvResult.setText(isSuccess ? "老化测试成功" : "老化测试失败");
            tvResult.setTextColor(getResources().getColor(
                isSuccess ? android.R.color.holo_green_dark : android.R.color.holo_red_dark
            ));

            // 设置测试摘要，移除老化成功次数
            String summary = String.format(
                "设定时长：%d分钟\n实际运行：%d分钟\n",
                testDuration / 60,
                actualDuration / 60
            );
            tvSummary.setText(summary);

            // 确认按钮点击事件
            btnConfirm.setOnClickListener(v -> {
                dialog.dismiss();
                finish(); // 关闭活动
            });
            
            // 确保对话框随活动一起销毁
            dialog.setOnDismissListener(dialogInterface -> {
                if (dialog != null && dialog.isShowing()) {
                    dialog.dismiss();
                }
            });

            if (!isFinishing()) {  // 显示之前最后检查一次
                try {
                    dialog.show();
                } catch (Exception e) {
                    // 忽略显示对话框时的异常
                }
            }
        });
    }

    private void startStatusUpdate() {
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isTestRunning) {
                    updateTestStatus();
                    handler.postDelayed(this, 1000);
                }
            }
        }, 1000);
    }

    private void updateTestStatus() {
        long currentTime = System.currentTimeMillis();
        
        executorService.execute(() -> {
            final String systemInfo;
            if (currentTime - lastSystemInfoTime >= SYSTEM_INFO_INTERVAL) {
                systemInfo = getSystemInfo();
                lastSystemInfoTime = currentTime;
            } else {
                systemInfo = null;
            }

            runOnUiThread(() -> {
                long elapsedSeconds = (currentTime - startTime) / 1000;
                long remainingSeconds = testDuration - elapsedSeconds;

                // 修改这里：剩余时间为0时，将已运行时间设置为总测试时长
                if (remainingSeconds <= 0) {
                    elapsedSeconds = testDuration;
                    remainingSeconds = 0;
                }

                // 更新状态显示
                String statusStr = String.format(Locale.getDefault(),
                        "<font color='#4CAF50'><b>已运行: %02d:%02d:%02d</b></font> / 剩余: %02d:%02d:%02d",
                        elapsedSeconds / 3600, (elapsedSeconds % 3600) / 60, elapsedSeconds % 60,
                        remainingSeconds / 3600, (remainingSeconds % 3600) / 60, remainingSeconds % 60);

                // 更新状态时启用HTML格式
                tvAgingStatus.setText(android.text.Html.fromHtml(statusStr));
                
                if (systemInfo != null) {
                    appendLog(systemInfo);
                    if (tvSystemInfo != null) {
                        tvSystemInfo.setText(systemInfo);
                    }
                }

                // 在显示最终时间后再停止测试
                if (remainingSeconds <= 0) {
                    // 添加短暂延迟以确保最终时间能够显示
                    handler.postDelayed(() -> stopAgingTest(), 100);
                }
            });
        });
    }

    private void updateStatus(final String status) {
        Message msg = handler.obtainMessage(MSG_UPDATE_STATUS, status);
        handler.sendMessage(msg);
    }

    private void appendLog(final String log) {
        String timestamp = sdf.format(new Date());
        String logLine = timestamp + ": " + log + "\n";

        // 更新UI（仅当handler已初始化时）
        if (handler != null && enableFileLogging) {
            Message msg = handler.obtainMessage(MSG_APPEND_LOG, logLine);
            handler.sendMessage(msg);
        }

        // 缓冲日志
        synchronized (logBuffer) {
            if (logBuffer != null) {  // 添加空指针检查
                logBuffer.append(logLine);
                if (logBuffer.length() >= LOG_BUFFER_SIZE) {
                    flushLogBuffer();
                }
            }
        }
    }

    private void flushLogBuffer() {
        if (enableFileLogging && logFile != null && logBuffer.length() > 0) {
            final String logsToWrite;
            synchronized (logBuffer) {
                logsToWrite = logBuffer.toString();
                logBuffer.setLength(0);
            }

            executorService.execute(() -> {
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(logFile, true))) {
                    writer.write(logsToWrite);
                    writer.flush();
                } catch (IOException e) {
                    handler.post(() -> appendLog("日志写入失败: " + e.getMessage()));
                }
            });
        }
    }

    private void startDownloadTest() {
        downloadThread = new Thread(() -> {
            int retryCount = 0;
            final int MAX_RETRIES = 3;

            while (isDownloading) {
                try {
                    // 检查是否到达下载间隔时间
                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastDownloadTime < DOWNLOAD_INTERVAL) {
                        // 未到达间隔时间，休眠1秒后继续检查
                        Thread.sleep(1000);
                        continue;
                    }

                    // 执行下载
                    downloadFile();
                    downloadCount++;
                    retryCount = 0; // 重置重试计数
                    lastDownloadTime = System.currentTimeMillis(); // 更新最后下载时间

                    handler.post(() -> {
                        updateDownloadCount(downloadCount);
                        appendLog("完成第 " + downloadCount + " 次下载，下次下载将在5分钟后开始");
                    });

                } catch (Exception e) {
                    if (!isDownloading) break;

                    retryCount++;
                    final String errorMessage = e.getMessage();

                    if (retryCount >= MAX_RETRIES) {
                        handler.post(() -> appendLog("下载失败次数过多，等待10秒后重试: " + errorMessage));
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException ie) {
                            break;
                        }
                        retryCount = 0;
                    } else {
                        handler.post(() -> appendLog("下载出错，准备重试: " + errorMessage));
                        try {
                            Thread.sleep(1000 * retryCount);
                        } catch (InterruptedException ie) {
                            break;
                        }
                    }
                }
            }
        });
        downloadThread.start();
    }

    private void downloadFile() throws Exception {
        String downloadUrl = isHighLevel ? DOWNLOAD_URL_HIGH : DOWNLOAD_URL_LOW;
        URL url = new URL(downloadUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.connect();

        int fileLength = connection.getContentLength();
        File downloadDir = new File(getExternalFilesDir(null), DOWNLOAD_DIR);
        if (!downloadDir.exists()) {
            downloadDir.mkdirs();
        }
        File outputFile = new File(downloadDir, "test_file_" + System.currentTimeMillis());

        try (BufferedInputStream input = new BufferedInputStream(connection.getInputStream(), BUFFER_SIZE);
             BufferedOutputStream output = new BufferedOutputStream(new FileOutputStream(outputFile), BUFFER_SIZE)) {

            byte[] data = new byte[BUFFER_SIZE];
            long total = 0;
            int count;
            long lastUpdateTime = System.currentTimeMillis();

            while ((count = input.read(data)) != -1 && isDownloading) {
                total += count;
                output.write(data, 0, count);

                // 降低进度更新频率
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastUpdateTime >= PROGRESS_UPDATE_INTERVAL) {
                    final int progress = (int) (total * 100 / fileLength);
                    handler.sendMessage(handler.obtainMessage(MSG_UPDATE_PROGRESS, progress, 0));
                    lastUpdateTime = currentTime;
                }
            }
            
            // 确保最后一次进度更新为100%
            if (isDownloading) {
                handler.sendMessage(handler.obtainMessage(MSG_UPDATE_PROGRESS, 100, 0));
            }
            
            // 强制写入缓冲区
            output.flush();
        } finally {
            // 删除下载的文件并记录日志
            if (outputFile.exists()) {
                boolean deleteSuccess = outputFile.delete();
                runOnUiThread(() -> {
                    if (deleteSuccess) {
                        appendLog("临时文件删除成功: " + outputFile.getName());
                    } else {
                        appendLog("临时文件删除失败: " + outputFile.getName());
                    }
                });
            }
            connection.disconnect();
        }
    }

    private void updateDownloadCount(final int count) {
        Message msg = handler.obtainMessage(MSG_UPDATE_DOWNLOAD_COUNT);
        msg.arg1 = count;
        handler.sendMessage(msg);
    }

    private void initLogFile() {
        File logDir = new File(getExternalFilesDir(null), LOG_DIR);
        if (!logDir.exists()) {
            logDir.mkdirs();
        }

        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
                .format(new Date());
        logFile = new File(logDir, "aging_test_" + timestamp + ".log");

        try {
            if (!logFile.exists()) {
                logFile.createNewFile();
            }
            appendLog("日志文件创建成功: " + logFile.getAbsolutePath());
        } catch (IOException e) {
            appendLog("日志文件创建失败: " + e.getMessage());
        }
    }

    private String getSystemInfo() {
        StringBuilder info = new StringBuilder();

        // CPU温度
        try {
            BufferedReader reader = new BufferedReader(new FileReader("/sys/class/thermal/thermal_zone0/temp"));
            float temp = Integer.parseInt(reader.readLine()) / 1000.0f;
            info.append(String.format("CPU温度: %.1f°C, ", temp));
            reader.close();
        } catch (Exception e) {
            info.append("CPU温度: 无法获取, ");
        }

        // CPU使率
        try {
            BufferedReader reader = new BufferedReader(new FileReader("/proc/stat"));
            String line = reader.readLine();
            String[] cpuInfo = line.split("\\s+");
            long totalCpu = 0;
            for (int i = 1; i < cpuInfo.length; i++) {
                totalCpu += Long.parseLong(cpuInfo[i]);
            }
            long idleCpu = Long.parseLong(cpuInfo[4]);
            float cpuUsage = 100.0f * (1.0f - (float)idleCpu/totalCpu);
            info.append(String.format("CPU使用率: %.1f%%, ", cpuUsage));
            reader.close();
        } catch (Exception e) {
            info.append("CPU使用率: 无法获取, ");
        }

        // 内存信息
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);
        float totalMem = memoryInfo.totalMem / (1024.0f * 1024.0f * 1024.0f);
        float availMem = memoryInfo.availMem / (1024.0f * 1024.0f * 1024.0f);
        float memUsage = (totalMem - availMem) / totalMem * 100;
        info.append(String.format("内存使用率: %.1f%%, ", memUsage));

        // 电池信息
        IntentFilter ifilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatus = registerReceiver(null, ifilter);
        
        int level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
        int scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
        float batteryPct = level * 100 / (float)scale;
        
        float batteryTemp = batteryStatus.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, 0) / 10.0f;
        
        info.append(String.format("电池电量: %.1f%%, ", batteryPct));
        info.append(String.format("电池温度: %.1f°C", batteryTemp));

        return info.toString();
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 停止音乐播放
        Log.d("coco","老化 onPause----");
        if (mediaPlayer != null && mediaPlayer.isPlaying()) {
            mediaPlayer.pause();
            Log.d("coco","老化 onPause--暂停音乐播放--");
            appendLog("暂停音乐播放");
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d("coco","老化 onResume----");
        if (isTestRunning && mediaPlayer != null) {
            try {
                if (mediaPlayer.getCurrentPosition() > 0) {
                    // 如果有播放位置，说明是暂停状态，继续播放
                    mediaPlayer.start();
                    Log.d("coco","老化 onResume--继续播放音乐--");
                    appendLog("继续播放音乐");
                } else {
                    // 如果没有播放位置，从头开始播放
                    mediaPlayer.seekTo(0);
                    mediaPlayer.start();
                    Log.d("coco","老化 onResume--重新播放音乐--");
                    appendLog("重新播放音乐");
                }
            } catch (Exception e) {
                Log.e("coco", "老化 onResume--播放音乐失败: " + e.getMessage());
                appendLog("播放音乐失败: " + e.getMessage());
            }
        }
    }

    @Override
    protected void onDestroy() {
        // 清除永久亮屏标志
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        
        appendLog("测试活动结束");

        // 确保在销毁前停止所有活动，但不显示结果对话框
        if (isTestRunning) {
            isTestRunning = false;
            isDownloading = false;
            if (downloadThread != null) {
                downloadThread.interrupt();
                downloadThread = null;
            }
            if (mediaPlayer != null && mediaPlayer.isPlaying()) {
                mediaPlayer.stop();
            }
        }

        super.onDestroy();

        if (mediaPlayer != null) {
            mediaPlayer.release();
            mediaPlayer = null;
        }

        // 确保所有日志都被写入
        flushLogBuffer();

        // 关闭线程池
        if (executorService != null) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(2, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
            }
        }
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        // 保存测试状态
        outState.putBoolean("isTestRunning", isTestRunning);
        outState.putBoolean("isDownloading", isDownloading);
        outState.putInt("downloadCount", downloadCount);
        // ... 其他状态保存
    }

    private void startNetworkStressTest() {
        Intent intent = new Intent(this, NetworkStressTestActivity.class);
        startActivity(intent);
    }
}