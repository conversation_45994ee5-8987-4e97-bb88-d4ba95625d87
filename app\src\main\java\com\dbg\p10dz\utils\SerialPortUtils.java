package com.dbg.p10dz.utils;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.wsm.comlib.SerialPortManager;
import com.wsm.comlib.callback.SerialPortDataListener;
import com.wsm.comlib.callback.SerialPortOpenListener;
import com.wsm.comlib.util.HexUtil;

import static com.wsm.comlib.constant.ConnectCostant.*;



//***********************

/*
// 创建工具类实例
SerialPortUtils serialPort = new SerialPortUtils(context, new SerialPortUtils.SerialPortCallback() {
    @Override
    public void onConnected() {
        Log.d(TAG, "设备已连接");
    }

    @Override
    public void onDisconnected() {
        Log.d(TAG, "设备已断开");
    }

    @Override
    public void onDataReceived(String data) {
        Log.d(TAG, "收到数据: " + data);
    }

    @Override
    public void onError(String error) {
        Log.e(TAG, "错误: " + error);
    }
});

// 连接设备
serialPort.connect(0x0483, 0x5740);  // 替换为实际的VID和PID

// 发送数据
serialPort.sendHexData("010203");  // 发送16进制数据
serialPort.sendTextData("Hello");  // 发送文本数据

// 断开连接
serialPort.disconnect();
*/

//**********************


/**
 * 串口通信工具类
 */
public class SerialPortUtils {
    private static final String TAG = "SerialPortUtils";

    private Context mContext;
    private SerialPortManager serialPortManager;
    private SerialPortCallback mCallback;
    private boolean isConnected = false;
    private SerialPortOpenListener mSerialPortOpenListener;
    private SerialPortDataListener mSerialPortDataListener;

    public interface SerialPortCallback {
        void onConnected();
        void onDisconnected();
        void onDataReceived(String data);
        void onError(String error);
    }

    public SerialPortUtils(Context context, SerialPortCallback callback) {
        this.mContext = context;
        this.mCallback = callback;
        this.serialPortManager = SerialPortManager.getInstance();
        initSerialPort();
    }

    private void initSerialPort() {
        // 启用日志
        serialPortManager.enableLog(true);

        // 设置连接监听
        SerialPortOpenListener openListener = new SerialPortOpenListener() {
            @Override
            public void onConnectStatusChange(int status) {
                switch (status) {
                    case ACTION_USB_PERMISSION_GRANTED:
                        isConnected = true;
                        if(mCallback != null) mCallback.onConnected();
                        break;
                    case ACTION_USB_PERMISSION_NOT_GRANTED:
                        isConnected = false;
                        if(mCallback != null) mCallback.onError("USB权限未授予");
                        break;
                    case ACTION_NO_USB:
                        isConnected = false;
                        if(mCallback != null) mCallback.onError("未连接USB设备");
                        break;
                    case ACTION_USB_DISCONNECTED:
                        isConnected = false;
                        if(mCallback != null) mCallback.onDisconnected();
                        break;
                    case ACTION_USB_NOT_SUPPORTED:
                        isConnected = false;
                        if(mCallback != null) mCallback.onError("不支持的USB设备");
                        break;
                }
            }
        };

        // 设置数据监听
        SerialPortDataListener dataListener = new SerialPortDataListener() {
            @Override
            public void onDataReceived(byte status, String data) {
                if (!TextUtils.isEmpty(data) && mCallback != null) {
                    mCallback.onDataReceived(data);
                }
            }

            @Override
            public void onOriginalDataReceived(byte status, byte[] bytes, int length) {
                String hexData = HexUtil.formatHexString(bytes);
                Log.d(TAG, "收到原始数据: " + hexData);
            }
        };

        // 保存监听器引用
        this.mSerialPortOpenListener = openListener;
        this.mSerialPortDataListener = dataListener;
    }

    /**
     * 连接指定VID/PID的设备
     */
    public boolean connect(int vid, int pid) {
        if(isConnected) {
            Log.w(TAG, "已经连接,请先断开连接");
            return false;
        }

        // 设置设备ID
        boolean setResult = serialPortManager.setDeviceID(vid, pid);
        if(!setResult) {
            Log.e(TAG, "设置设备ID失败");
            return false;
        }

        // 打开串口
        serialPortManager.openSerialPort(mContext, mSerialPortOpenListener, mSerialPortDataListener);
        return true;
    }

    /**
     * 断开连接
     */
    public void disconnect() {
        if(serialPortManager != null) {
            serialPortManager.closeSerialPort();
            isConnected = false;
        }
    }

    /**
     * 发送16进制数据
     */
    public boolean sendHexData(String hexData) {
        if(!isConnected) {
            Log.e(TAG, "未连接设备");
            return false;
        }

        if(TextUtils.isEmpty(hexData)) {
            Log.e(TAG, "发送数据为空");
            return false;
        }

        // 验证16进制格式
        hexData = hexData.trim();
        if(hexData.length() % 2 != 0) {
            Log.e(TAG, "非法的16进制数据长度");
            return false;
        }

        for(int i = 0; i < hexData.length(); i++) {
            char c = hexData.charAt(i);
            if(!(c >= '0' && c <= '9') &&
                    !(c >= 'a' && c <= 'f') &&
                    !(c >= 'A' && c <= 'F')) {
                Log.e(TAG, "非法的16进制字符");
                return false;
            }
        }

        // 发送数据
        serialPortManager.sendData(hexData);
        return true;
    }

    /**
     * 发送文本数据
     */
    public boolean sendTextData(String text) {
        if(!isConnected) {
            Log.e(TAG, "未连接设备");
            return false;
        }

        if(TextUtils.isEmpty(text)) {
            Log.e(TAG, "发送数据为空");
            return false;
        }

        serialPortManager.sendData(HexUtil.formatHexString(text.getBytes()));
        return true;
    }

    /**
     * 设置超时时间
     */
    public boolean setTimeout(int timeout) {
        return serialPortManager.setPackageTimeOut(timeout);
    }

    /**
     * 是否已连接
     */
    public boolean isConnected() {
        return isConnected;
    }
}