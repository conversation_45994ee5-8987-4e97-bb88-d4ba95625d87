package com.dbg.p10dz;

import android.Manifest;
import android.annotation.SuppressLint;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.AssetFileDescriptor;
import android.media.MediaPlayer;
import android.os.Build;
import android.util.Log;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;

import java.lang.reflect.Method;
import java.util.Set;

/**
 * 系统级蓝牙管理器 - 实现无感连接
 * 适用于具有系统签名的预装应用
 */
public class SystemBtManager {
    private TextView textView;
    private Context context;
    private MediaPlayer mediaPlayer;
    private BluetoothAdapter bluetoothAdapter;
    private static final String TARGET_DEVICE_NAME = "BlueCash-05 cradle";
    private static final String TAG = "SystemBtManager";
    
    private final BroadcastReceiver bluetoothReceiver = new BroadcastReceiver() {
        @SuppressLint("MissingPermission")
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                if (device != null && TARGET_DEVICE_NAME.equals(device.getName())) {
                    Log.d(TAG, "找到目标设备: " + device.getName());
                    bluetoothAdapter.cancelDiscovery();
                    // 系统级应用可以直接创建绑定，无需用户确认
                    device.createBond();
                }
                updateTextView(device);
            } else if (BluetoothDevice.ACTION_BOND_STATE_CHANGED.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                int bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, -1);
                if (device != null && TARGET_DEVICE_NAME.equals(device.getName())) {
                    if (bondState == BluetoothDevice.BOND_BONDED) {
                        Log.d(TAG, "配对成功，播放音乐");
                        playMusic();
                    }
                }
            }
        }
    };

    public SystemBtManager(TextView textView) {
        this.textView = textView;
    }

    /**
     * 系统级应用初始化蓝牙流程 - 无需用户交互
     */
    public void initSystemBluetoothFlow(AppCompatActivity activity) {
        this.context = activity;
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

        if (bluetoothAdapter == null) {
            Log.e(TAG, "设备不支持蓝牙");
            return;
        }

        // 注册广播
        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothDevice.ACTION_FOUND);
        filter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        activity.registerReceiver(bluetoothReceiver, filter);

        // 系统应用直接开始蓝牙流程，无需权限检查
        startSystemBluetoothFlow();
    }

    /**
     * 系统级蓝牙流程 - 自动开启蓝牙并连接
     */
    @SuppressLint("MissingPermission")
    private void startSystemBluetoothFlow() {
        // 系统应用可以直接开启蓝牙，无需用户确认
        if (!bluetoothAdapter.isEnabled()) {
            Log.d(TAG, "系统级开启蓝牙");
            try {
                // 使用反射调用系统级方法强制开启蓝牙
                Method enableMethod = bluetoothAdapter.getClass().getMethod("enable");
                boolean result = (Boolean) enableMethod.invoke(bluetoothAdapter);
                if (result) {
                    Log.d(TAG, "蓝牙开启成功");
                    // 等待蓝牙完全开启
                    waitForBluetoothEnabled();
                } else {
                    Log.e(TAG, "蓝牙开启失败");
                }
            } catch (Exception e) {
                Log.e(TAG, "反射开启蓝牙失败，尝试标准方法", e);
                // 如果反射失败，使用标准方法（但仍然是系统级权限）
                bluetoothAdapter.enable();
                waitForBluetoothEnabled();
            }
        } else {
            checkAndConnect();
        }
    }

    /**
     * 等待蓝牙完全开启
     */
    private void waitForBluetoothEnabled() {
        new Thread(() -> {
            int attempts = 0;
            while (attempts < 50 && !bluetoothAdapter.isEnabled()) { // 最多等待5秒
                try {
                    Thread.sleep(100);
                    attempts++;
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
            
            if (bluetoothAdapter.isEnabled()) {
                Log.d(TAG, "蓝牙已完全开启");
                // 在主线程中继续流程
                if (context instanceof AppCompatActivity) {
                    ((AppCompatActivity) context).runOnUiThread(this::checkAndConnect);
                }
            } else {
                Log.e(TAG, "蓝牙开启超时");
            }
        }).start();
    }

    /**
     * 检查并连接目标设备
     */
    @SuppressLint("MissingPermission")
    private void checkAndConnect() {
        if (isTargetDeviceBonded()) {
            Log.d(TAG, "目标设备已配对，直接播放音乐");
            playMusic();
        } else {
            Log.d(TAG, "开始扫描目标设备");
            startScan();
        }
    }

    @SuppressLint("MissingPermission")
    private boolean isTargetDeviceBonded() {
        Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
        for (BluetoothDevice device : bondedDevices) {
            if (TARGET_DEVICE_NAME.equals(device.getName())) {
                return true;
            }
        }
        return false;
    }

    @SuppressLint("MissingPermission")
    private void startScan() {
        if (bluetoothAdapter.isDiscovering()) {
            bluetoothAdapter.cancelDiscovery();
        }
        Log.d(TAG, "开始扫描设备");
        bluetoothAdapter.startDiscovery();
    }

    private void updateTextView(BluetoothDevice device) {
        if (device != null && textView != null) {
            @SuppressLint("MissingPermission")
            String deviceName = device.getName();
            Log.d(TAG, "扫描到设备: " + deviceName);
            if (context instanceof AppCompatActivity) {
                ((AppCompatActivity) context).runOnUiThread(() -> 
                    textView.append("找到设备: " + deviceName + "\n"));
            }
        }
    }

    private void playMusic() {
        try {
            if (mediaPlayer != null) {
                mediaPlayer.release();
                mediaPlayer = null;
            }
            mediaPlayer = new MediaPlayer();
            AssetFileDescriptor afd = context.getResources().openRawResourceFd(R.raw.gee);
            mediaPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(), afd.getLength());
            afd.close();

            mediaPlayer.prepare();
            mediaPlayer.start();

            mediaPlayer.setOnCompletionListener(mp -> {
                Log.d(TAG, "播放完成，释放资源");
                stopMusic();
                unpairDevice();
            });
            
            Log.d(TAG, "音乐播放开始");
        } catch (Exception e) {
            Log.e(TAG, "播放音乐失败", e);
        }
    }

    public void stopMusic() {
        if (mediaPlayer != null) {
            try {
                if (mediaPlayer.isPlaying()) {
                    mediaPlayer.stop();
                }
                mediaPlayer.release();
                mediaPlayer = null;
                Log.d(TAG, "音乐播放停止");
            } catch (Exception e) {
                Log.e(TAG, "停止音乐失败", e);
            }
        }
    }

    @SuppressLint("MissingPermission")
    public void unpairDevice() {
        Set<BluetoothDevice> bondedDevices = bluetoothAdapter.getBondedDevices();
        for (BluetoothDevice device : bondedDevices) {
            if (TARGET_DEVICE_NAME.equals(device.getName())) {
                try {
                    Method removeBondMethod = device.getClass().getMethod("removeBond");
                    removeBondMethod.invoke(device);
                    Log.d(TAG, "取消配对成功");
                } catch (Exception e) {
                    Log.e(TAG, "取消配对失败", e);
                }
                break;
            }
        }
        
        // 系统应用可以直接关闭蓝牙
        if (bluetoothAdapter.isEnabled()) {
            try {
                Method disableMethod = bluetoothAdapter.getClass().getMethod("disable");
                boolean result = (Boolean) disableMethod.invoke(bluetoothAdapter);
                if (result) {
                    Log.d(TAG, "蓝牙已关闭");
                }
            } catch (Exception e) {
                Log.e(TAG, "反射关闭蓝牙失败，使用标准方法", e);
                bluetoothAdapter.disable();
            }
        }
    }

    /**
     * 系统级应用权限检查 - 始终返回true
     */
    public boolean hasSystemPermissions() {
        // 系统签名应用自动拥有所有权限
        return true;
    }

    /**
     * 检查是否为系统应用
     */
    public boolean isSystemApp() {
        if (context == null) return false;
        try {
            int flags = context.getPackageManager().getApplicationInfo(context.getPackageName(), 0).flags;
            return (flags & android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0;
        } catch (Exception e) {
            Log.e(TAG, "检查系统应用状态失败", e);
            return false;
        }
    }

    public void release() {
        try {
            if (context != null) {
                context.unregisterReceiver(bluetoothReceiver);
            }
        } catch (Exception e) {
            Log.e(TAG, "广播取消注册失败", e);
        }
        stopMusic();
    }
}