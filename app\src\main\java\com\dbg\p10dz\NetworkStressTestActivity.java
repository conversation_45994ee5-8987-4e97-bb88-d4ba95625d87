package com.dbg.p10dz;

import android.graphics.Color;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.view.MenuItem;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.util.Log;
import androidx.appcompat.app.AppCompatActivity;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.CountDownLatch;

public class NetworkStressTestActivity extends AppCompatActivity {
    private static final String TAG = "coco";
    private static final String TEST_URL = "http://*************:5000/test/release/st30m/ST30M.USERDEBUG.64.13.H3.0_V1.0.8_20240723.1716.zip";
    private static final long TARGET_TOTAL_BYTES = 10L * 1024 * 1024 * 1024 * 1024; // 10TB
    private static final String LOG_DIR = "network_stress_test_logs";
    private static final int MAX_LOG_LINES = 100; // 最大显示日志行数
    private static final long UI_UPDATE_INTERVAL = 100; // 100ms更新一次UI

    private StringBuilder logBuffer = new StringBuilder();
    private LinkedBlockingQueue<String> logQueue = new LinkedBlockingQueue<>();
    private volatile boolean isLogProcessorRunning = true;
    private long lastUIUpdateTime = 0;

    private ProgressBar totalProgressBar;
    private ProgressBar currentFileProgressBar; 
    private TextView totalProgressText;
    private TextView currentFileProgressText;
    private TextView statsTextView;
    private TextView logTextView;

    private File logFile;
    private long totalDownloadedBytes = 0;
    private int successCount = 0;
    private int failureCount = 0;
    private volatile boolean isTestRunning = false;
    private ThreadPoolExecutor downloadExecutor;
    private ExecutorService logExecutor;
    private Handler mainHandler;

    private static final int MSG_UPDATE_TOTAL_PROGRESS = 1;
    private static final int MSG_UPDATE_CURRENT_PROGRESS = 2;
    private static final int MSG_UPDATE_STATS = 3;
    private static final int MSG_APPEND_LOG = 4;

    private final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(Message msg) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastUIUpdateTime < UI_UPDATE_INTERVAL) {
                return;
            }
            lastUIUpdateTime = currentTime;

            switch (msg.what) {
                case MSG_UPDATE_TOTAL_PROGRESS:
                    int totalProgress = msg.arg1;
                    totalProgressBar.setProgress(totalProgress);
                    totalProgressText.setText(totalProgress + "%");
                    break;
                case MSG_UPDATE_CURRENT_PROGRESS:
                    int currentProgress = msg.arg1;
                    currentFileProgressBar.setProgress(currentProgress);
                    currentFileProgressText.setText(currentProgress + "%");
                    break;
                case MSG_UPDATE_STATS:
                    updateStats();
                    break;
                case MSG_APPEND_LOG:
                    String log = (String) msg.obj;
                    appendToLog(log);
                    break;
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate");
        setContentView(R.layout.activity_network_stress_test);

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }

        mainHandler = new Handler(Looper.getMainLooper());
        Log.d(TAG, "初始化Handler完成");
    
        // 初始化优化的线程池
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        int maximumPoolSize = corePoolSize * 2;
        downloadExecutor = new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1024),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
        Log.d(TAG, "初始化下载线程池完成: 核心线程数=" + corePoolSize + ", 最大线程数=" + maximumPoolSize);

        logExecutor = Executors.newSingleThreadExecutor();
        startLogProcessor();
        Log.d(TAG, "初始化日志线程池完成");

        initViews();
        initLogFile();
        Log.d(TAG, "初始化视图和日志文件完成");
    
        // 设置TextView的最大行数
        logTextView.setMaxLines(MAX_LOG_LINES);
        
        // 初始化显示成功率
        updateStats();
    
        startStressTest();
        Log.d(TAG, "启动压力测试");
    }

    private void initViews() {
        totalProgressBar = findViewById(R.id.totalProgressBar);
        currentFileProgressBar = findViewById(R.id.currentFileProgressBar);
        totalProgressText = findViewById(R.id.totalProgressText);
        currentFileProgressText = findViewById(R.id.currentFileProgressText);
        statsTextView = findViewById(R.id.statsTextView);
        logTextView = findViewById(R.id.logTextView);

        totalProgressBar.setMax(100);
        currentFileProgressBar.setMax(100);
    }

    private void initLogFile() {
        File logDir = new File(getExternalFilesDir(null), LOG_DIR);
        if (!logDir.exists()) {
            logDir.mkdirs();
        }

        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())
                .format(new Date());
        logFile = new File(logDir, "network_stress_" + timestamp + ".log");

        try {
            if (!logFile.exists()) {
                logFile.createNewFile();
            }
            writeLog("测试开始时间: " + timestamp);
        } catch (IOException e) {
            appendToLog("日志文件创建失败: " + e.getMessage());
        }
    }

    private void startStressTest() {
        isTestRunning = true;
        Log.d(TAG, "开始压力测试，目标下载量: " + (TARGET_TOTAL_BYTES / 1024 / 1024 / 1024) + "GB");
        
        downloadExecutor.execute(() -> {
            int downloadCount = 1;
            while (isTestRunning && totalDownloadedBytes < TARGET_TOTAL_BYTES) {
                try {
                    Log.d(TAG, "开始第 " + downloadCount + " 轮下载");
                    boolean success = downloadFile(downloadCount);
                    Log.d(TAG, "第 " + downloadCount + " 轮下载" + (success ? "成功" : "失败"));
                    
                    if (success) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                    
                    Log.d(TAG, String.format("总进度: %.2f%%, 成功: %d, 失败: %d",
                        (totalDownloadedBytes * 100.0 / TARGET_TOTAL_BYTES),
                        successCount, failureCount));
                    
                    handler.sendEmptyMessage(MSG_UPDATE_STATS);
                    downloadCount++;
                    
                } catch (Exception e) {
                    Log.e(TAG, "下载异常: " + e.getMessage(), e);
                }
            }
            Log.d(TAG, "压力测试结束");
        });
    }

    private boolean downloadFile(int downloadCount) {
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        try {
            Log.d(TAG, "开始连接URL: " + TEST_URL);
            URL url = new URL(TEST_URL);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);
            
            int responseCode = connection.getResponseCode();
            Log.d(TAG, "HTTP响应码: " + responseCode);
            
            if (responseCode != HttpURLConnection.HTTP_OK) {
                Log.e(TAG, "下载请求失败: " + responseCode);
                return false;
            }

            long fileLength = connection.getContentLength();
            Log.d(TAG, "文件大小: " + (fileLength / 1024 / 1024) + "MB");
            
            long downloadedLength = 0;
            byte[] buffer = new byte[1024 * 1024]; // 1MB缓冲区
            int bytes;

            Log.d(TAG, "开始读取文件流");
            inputStream = connection.getInputStream();
            long startTime = System.currentTimeMillis();
            long lastLogTime = startTime;
            long lastProgressTime = startTime;
            
            while ((bytes = inputStream.read(buffer)) != -1 && isTestRunning) {
                downloadedLength += bytes;
                totalDownloadedBytes += bytes;

                // 每100ms更新一次进度
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastProgressTime >= 100) {
                    // 更新进度
                    int currentProgress = (int) (downloadedLength * 100 / fileLength);
                    int totalProgress = (int) (totalDownloadedBytes * 100 / TARGET_TOTAL_BYTES);
                    
                    Message currentMsg = handler.obtainMessage(MSG_UPDATE_CURRENT_PROGRESS);
                    currentMsg.arg1 = currentProgress;
                    handler.sendMessage(currentMsg);

                    Message totalMsg = handler.obtainMessage(MSG_UPDATE_TOTAL_PROGRESS);
                    totalMsg.arg1 = totalProgress;
                    handler.sendMessage(totalMsg);

                    lastProgressTime = currentTime;
                }

                // 每5秒记录一次下载速度
                if (currentTime - lastLogTime >= 5000) {
                    double speed = (downloadedLength / 1024.0 / 1024.0) / ((currentTime - startTime) / 1000.0);
                    double totalGB = totalDownloadedBytes / (1024.0 * 1024.0 * 1024.0);
                    String logMessage = String.format(Locale.getDefault(), 
                        "%d次下载，速度: %.2f MB/s，总计: %.2f GB", 
                        downloadCount, speed, totalGB);
                    Log.d(TAG, logMessage);
                    writeLog(logMessage);
                    lastLogTime = currentTime;
                }
            }

            long endTime = System.currentTimeMillis();
            double totalTime = (endTime - startTime) / 1000.0;
            double avgSpeed = (downloadedLength / 1024.0 / 1024.0) / totalTime;
            double totalGB = totalDownloadedBytes / (1024.0 * 1024.0 * 1024.0);
            String completeMessage = String.format(Locale.getDefault(),
                "%d次下载完成，速度: %.2f MB/s，总计: %.2f GB",
                downloadCount, avgSpeed, totalGB);
            Log.d(TAG, completeMessage);
            writeLog(completeMessage);

            return downloadedLength == fileLength;
            
        } catch (IOException e) {
            Log.e(TAG, "下载出错: " + e.getMessage(), e);
            return false;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "关闭输入流失败: " + e.getMessage(), e);
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    private void writeLog(final String message) {
        if (logExecutor.isShutdown()) return;
        
        String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                .format(new Date());
        String logMessage = timestamp + " - " + message + "\n";
        
        // 添加到日志队列
        logQueue.offer(logMessage);
    }

    private void updateStats() {
        int total = successCount + failureCount;
        float successRate = total > 0 ? (float) successCount / total * 100 : 0;
        String stats = String.format(Locale.getDefault(),
                "成功: %d\n失败: %d\n成功率: %.1f%%",
                successCount, failureCount, successRate);
        statsTextView.setText(stats);
    }

    private void appendToLog(String message) {
        // 添加到缓冲区
        logBuffer.append(message);

        // 如果内容太长，移除旧的日志
        String[] lines = logBuffer.toString().split("\n");
        if (lines.length > MAX_LOG_LINES) {
            logBuffer = new StringBuilder();
            for (int i = lines.length - MAX_LOG_LINES; i < lines.length; i++) {
                logBuffer.append(lines[i]).append("\n");
            }
        }

        // 更新TextView并滚动到底部
        logTextView.setText(logBuffer.toString());
        // 使用post确保在布局完成后滚动到底部
        logTextView.post(() -> {
            int lineCount = logTextView.getLineCount();
            if (lineCount > 0) {
                int scrollAmount = logTextView.getLayout().getLineTop(lineCount) - logTextView.getHeight();
                if (scrollAmount > 0) {
                    logTextView.scrollTo(0, scrollAmount);
                } else {
                    logTextView.scrollTo(0, 0);
                }
            }
        });
    }

    private void startLogProcessor() {
        logExecutor.execute(() -> {
            while (isLogProcessorRunning) {
                try {
                    String logMessage = logQueue.poll(100, TimeUnit.MILLISECONDS);
                    if (logMessage != null) {
                        // 写入文件
                        try (FileWriter writer = new FileWriter(logFile, true)) {
                            writer.append(logMessage);
                        } catch (IOException e) {
                            Log.e(TAG, "写入日志文件失败", e);
                        }

                        // 更新UI
                        handler.post(() -> {
                            logBuffer.append(logMessage);
                            String[] lines = logBuffer.toString().split("\n");
                            if (lines.length > MAX_LOG_LINES) {
                                logBuffer = new StringBuilder();
                                for (int i = lines.length - MAX_LOG_LINES; i < lines.length; i++) {
                                    logBuffer.append(lines[i]).append("\n");
                                }
                            }
                            logTextView.setText(logBuffer.toString());
                        });
                    }
                } catch (InterruptedException e) {
                    Log.d(TAG, "日志处理线程被中断");
                    break;
                }
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy");
        isTestRunning = false;
        isLogProcessorRunning = false;
        
        if (downloadExecutor != null) {
            downloadExecutor.shutdown();
            try {
                if (!downloadExecutor.awaitTermination(2, TimeUnit.SECONDS)) {
                    downloadExecutor.shutdownNow();
                }
                Log.d(TAG, "下载线程池已关闭");
            } catch (InterruptedException e) {
                Log.e(TAG, "关闭下载线程池失败", e);
                downloadExecutor.shutdownNow();
            }
        }
        
        if (logExecutor != null) {
            logExecutor.shutdown();
            try {
                if (!logExecutor.awaitTermination(2, TimeUnit.SECONDS)) {
                    logExecutor.shutdownNow();
                }
                Log.d(TAG, "日志线程池已关闭");
            } catch (InterruptedException e) {
                Log.e(TAG, "关闭日志线程池失败", e);
                logExecutor.shutdownNow();
            }
        }
        
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
        if (handler != null) {
            handler.removeCallbacksAndMessages(null);
        }
        Log.d(TAG, "Handler消息已清空");
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();
    }
} 