package com.dbg.p10dz;

import androidx.appcompat.app.AppCompatActivity;

import android.hardware.usb.UsbManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.app.AlertDialog;
import android.widget.EditText;
import android.view.inputmethod.EditorInfo;
import android.view.KeyEvent;
import android.widget.Toast;
import android.view.Menu;
import android.view.MenuItem;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.HashMap;
import android.content.pm.PackageManager;

import androidx.appcompat.widget.SwitchCompat;

import android.content.SharedPreferences;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Map;

import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.ArrayAdapter;
import android.content.pm.ActivityInfo;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import android.graphics.Bitmap;
import android.widget.ImageView;
import android.widget.TextView;
import android.util.TypedValue;
import android.graphics.Typeface;
import com.google.zxing.EncodeHintType;
import java.lang.reflect.Method;

public class MainActivity extends AppCompatActivity {
    private Button mCharingBtn,mNetworkBtn,mScannerBtn,mBluetoothBtn;//lyc
    private BroadcastReceiver mChargingReceiver;
    private AlertDialog mScanDialog;
    private AlertDialog mLedCheckDialog;
    private AlertDialog mAgingSettingsDialog;
    private static final String EXPECTED_QR_CODE = "DBGSZ1234567890";
    //private static final String EXPECTED_QR_CODE = "6942550100394";
    private static final String TEST_URL = "https://www.jd.com";
    private boolean isNetworkTestPassed = false;
    private boolean isLedCheckPassed = false;
    private UsbManager mUsbManager;
    private SwitchCompat actionSwitch;
    private static final String PREF_NAME = "ScannerSettings";
    private static final String KEY_SCANNER_ENABLED = "scanner_enabled";
    private boolean isScannerEnabled = false;  // 默认关闭
    private static final String ETHERNET_INTERFACE = "eth0";
    private BroadcastReceiver mEthernetReceiver;
    private static final String[] AGING_DURATIONS = {
        "1分钟", "30分钟", "1小时", "2小时", "4小时", "8小时", "24小时"
    };
    private static final long[] AGING_DURATION_MINUTES = {
        1, 30, 60, 120, 240, 480, 1440
    };
    private AlertDialog mSuccessDialog;
    private static final String SUCCESS_QR_PREFIX = "DBGSZ";
    private boolean isAllTestsPassed = false;
    private static final long DOUBLE_CLICK_TIME_DELTA = 500; //双击时间窗口(毫秒)
    private long lastClickTime = 0;
    private int clickCount = 0;
    private Bitmap mCachedQRCode;  // 缓存的二维码图片
    private String mCachedMacAddress;  // 缓存的MAC地址
    private View mAgingDialogView; // 缓存对话框视图
    private RadioGroup mAgingLevelGroup; // 缓存RadioGroup
    private Spinner mAgingDurationSpinner; // 缓存Spinner
    private ArrayAdapter<String> mDurationAdapter; // 缓存适配器
//    private BluetoothUtils bluetoothUtils;

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);

        MenuItem menuItem = menu.findItem(R.id.menu_switch);
        View actionView = menuItem.getActionView();
        actionSwitch = actionView.findViewById(R.id.action_switch);

        // 添加扫码头文本的点击事件
        TextView tvScannerSwitch = actionView.findViewById(R.id.tv_scanner_switch);
        tvScannerSwitch.setOnClickListener(v -> {
            // 获取当前方向
            int currentOrientation = getRequestedOrientation();

            // 切换屏幕方向
            if (currentOrientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT) {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
            } else {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            }
        });

        getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS);
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);

        // 添加老化测试按钮点击事件
        Button btnAgingTest = actionView.findViewById(R.id.btn_aging_test);
        btnAgingTest.setOnClickListener(v -> {
            Log.d("AgingTest", "老化按钮被点击");
            showAgingSettingsDialog();
        });

        // 设置开关初始状态
        actionSwitch.setChecked(isScannerEnabled);

        actionSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            isScannerEnabled = isChecked;
            // 保存开关状态
            SharedPreferences.Editor editor = getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE).edit();
            editor.putBoolean(KEY_SCANNER_ENABLED, isChecked);
            editor.apply();

            mScannerBtn.setVisibility(isChecked ? View.VISIBLE : View.INVISIBLE);
            if (!isChecked && mScanDialog != null && mScanDialog.isShowing()) {
                mScanDialog.dismiss();
                mScanDialog = null;
            }
        });

        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 第一时间自动授予运行时权限（系统应用专用）
        Log.i("MainActivity", "=== P10DZ 应用启动，开始自动授予运行时权限 ===");
        PrivPermissionGranter.grantAll(this);

        // 显示权限授予状态的Toast提示
        showPermissionStatusToast();

        // 设置屏幕旋转180度
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN_MR2) {
            // 检查系统属性
            String debugMode = getSystemProperty("persist.dbg.debug", "0");
            if (!"1".equals(debugMode)) {
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
            }
        }

        setContentView(R.layout.activity_main);

        // 从SharedPreferences读取开关状态
        SharedPreferences prefs = getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        isScannerEnabled = prefs.getBoolean(KEY_SCANNER_ENABLED, false);


        Log.e("lyy-----", "初始化蓝牙");
        // 预先初始化老化测试对话框
        initAgingDialog();

        // 获取版本号并设置标题
        try {
            String versionName = getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
            String title = getString(R.string.app_name) + " v" + versionName;

            // 创建自定义标题View
            TextView titleView = new TextView(this);
            titleView.setText(title);
            titleView.setTextColor(Color.WHITE);
            titleView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 24);
            titleView.setPadding(0, 0, 0, 0);
            titleView.setTypeface(null, Typeface.BOLD);

            // 设置点击监听
            titleView.setOnClickListener(v -> {
                long clickTime = System.currentTimeMillis();
                if (clickTime - lastClickTime < DOUBLE_CLICK_TIME_DELTA) {
                    clickCount++;
                    if (clickCount >= 2) {
                        // 预先获取MAC地址并生成二维码
                        String macAddress = getMacAddr();
                        if (!macAddress.isEmpty()) {
                            String plainMacAddress = macAddress.replace(":", "");
                            generateQRCode(plainMacAddress);
                        }
                        showSuccessDialog();
                        clickCount = 0;
                    }
                } else {
                    clickCount = 1;
                }
                lastClickTime = clickTime;
            });

            // 设置自定义标题View
            if (getSupportActionBar() != null) {
                getSupportActionBar().setDisplayShowCustomEnabled(true);
                getSupportActionBar().setDisplayShowTitleEnabled(false);
                getSupportActionBar().setCustomView(titleView);
            }
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }

        layout_init();
        init();
        registerChargingReceiver();
        registerEthernetReceiver();
        
        // 在后台预先获取MAC地址
        new Thread(() -> {
            String macAddress = getMacAddr();
            if (!macAddress.isEmpty()) {
                String plainMacAddress = macAddress.replace(":", "");
                generateQRCode(plainMacAddress);
            }
        }).start();
    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d("coco","hxx----onResume----");

        // 每次恢复时检查权限状态，确保权限已正确授予
        PrivPermissionGranter.checkPermissionStatus(this);

        // 使用新的权限检查工具进行详细检查
        PermissionChecker.checkAllPermissions(this);

        // 在恢复时也检查一次以太网卡状态
        //if (isEthernetAvailable()) {
            startNetworkTest();
        //}
    }

    private void layout_init(){
        mCharingBtn = findViewById(R.id.charging_btn);
        mNetworkBtn = findViewById(R.id.network_btn);
        mScannerBtn = findViewById(R.id.scanner_btn);
        mBluetoothBtn=findViewById(R.id.bluetooth_btn);//lyc

        mCharingBtn.setOnClickListener(mOnClickListener);
        mNetworkBtn.setOnClickListener(mOnClickListener);
        mScannerBtn.setOnClickListener(mOnClickListener);
        mBluetoothBtn.setOnClickListener(mOnClickListener);

        // 根据开关状态设置扫码按钮可见性
        mScannerBtn.setVisibility(isScannerEnabled ? View.VISIBLE : View.INVISIBLE);
    }

    private void init(){
        // 检查当前充电状态
        IntentFilter ifilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatus = registerReceiver(null, ifilter);
        int status = batteryStatus.getIntExtra(android.os.BatteryManager.EXTRA_STATUS, -1);
        boolean isCharging = status == android.os.BatteryManager.BATTERY_STATUS_CHARGING ||
                status == android.os.BatteryManager.BATTERY_STATUS_FULL;
        Log.d("coco","hxx---init-----isCharging : " + isCharging);
        mCharingBtn.setBackgroundColor(isCharging ? Color.GREEN : Color.RED);

        mUsbManager = (UsbManager) getSystemService(Context.USB_SERVICE);

    }


    private View.OnClickListener mOnClickListener = new View.OnClickListener(){

        @Override
        public void onClick(View v) {
            int id = v.getId();
            if (id == R.id.charging_btn) {

            } else if(id == R.id.network_btn) {
                startNetworkTest();
            } else if(id == R.id.scanner_btn) {
                showScanDialog();
            } else if (id== R.id.bluetooth_btn){
                // 在启动蓝牙Activity之前，检查蓝牙权限状态
                Log.i("MainActivity", "蓝牙按钮被点击，检查蓝牙权限状态...");

                boolean bluetoothPermissionsOk = PermissionChecker.checkBluetoothPermissions(MainActivity.this);

                if (!bluetoothPermissionsOk) {
                    Log.w("MainActivity", "蓝牙权限不完整，尝试重新授予...");
                    PrivPermissionGranter.grantBluetoothPermissions(MainActivity.this);

                    // 显示权限状态提示
                    Toast.makeText(MainActivity.this, "正在授予蓝牙权限，请稍候...", Toast.LENGTH_SHORT).show();
                }

                // 短暂延迟后启动蓝牙Activity，确保权限检查和授予完成
                new android.os.Handler().postDelayed(() -> {
                    // 再次检查权限状态
                    boolean finalCheck = PermissionChecker.checkBluetoothPermissions(MainActivity.this);
                    if (!finalCheck) {
                        Toast.makeText(MainActivity.this, "蓝牙权限未完全授予，功能可能受限", Toast.LENGTH_LONG).show();
                    }

                    Intent intent = new Intent(MainActivity.this, BtActivity.class);
                    startActivity(intent);
                }, 800); // 延迟800ms
            }
        }
    };

    private void registerChargingReceiver() {
        mChargingReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action != null) {
                    if (action.equals(Intent.ACTION_POWER_CONNECTED)) {
                        mCharingBtn.setBackgroundColor(Color.GREEN);
                    } else if (action.equals(Intent.ACTION_POWER_DISCONNECTED)) {
                        mCharingBtn.setBackgroundColor(Color.RED);
                    }
                }
            }
        };

        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_POWER_CONNECTED);
        filter.addAction(Intent.ACTION_POWER_DISCONNECTED);
        registerReceiver(mChargingReceiver, filter);
    }

    private void registerEthernetReceiver() {
        Log.d("coco","hxx-----registerEthernetReceiver-----");
        mEthernetReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action != null && action.equals(UsbManager.ACTION_USB_DEVICE_ATTACHED)) {
                    // 当有USB设备连接时，检查是否是以太网卡
                    if (isEthernetAvailable()) {
                        startNetworkTest();
                    }
                }   
            }
        };

        IntentFilter filter = new IntentFilter();
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        registerReceiver(mEthernetReceiver, filter);

        Log.d("coco","hxx-----network test-----: " + isEthernetAvailable());
        // 检查当前是否已经有以太网卡
        if (isEthernetAvailable()) {
            startNetworkTest();
        }
    }

    private boolean isEthernetAvailable() {
        try {
            NetworkInterface intf = NetworkInterface.getByName(ETHERNET_INTERFACE);
            return intf != null && intf.isUp();
        } catch (SocketException e) {
            Log.e("MainActivity", "Error checking ethernet interface", e);
            return false;
        }
    }

    private void showScanDialog() {
        // 先关闭已存在的对话框
        if (mLedCheckDialog != null && mLedCheckDialog.isShowing()) {
            mLedCheckDialog.dismiss();
            mLedCheckDialog = null;
        }
        if (mScanDialog != null && mScanDialog.isShowing()) {
            mScanDialog.dismiss();
            mScanDialog = null;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = getLayoutInflater().inflate(R.layout.layout_dailog_scan, null);

        EditText qrCodeInput = dialogView.findViewById(R.id.qrcode_scan_input);

        // 添加禁止弹出输入法的代码
        qrCodeInput.setShowSoftInputOnFocus(false);

        builder.setTitle("扫码测试")
                .setView(dialogView)
                .setCancelable(false)
                .setNegativeButton("取消", (dialog, which) -> {
                    mScannerBtn.setBackgroundColor(Color.RED);
                });

        // 设置输入框属性
        qrCodeInput.setImeOptions(EditorInfo.IME_ACTION_DONE);  // 修改为完成动作

        // 修改二维码输入监听
        qrCodeInput.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || 
                (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                String qrResult = qrCodeInput.getText().toString().trim();
                handleScanResult(qrResult);
                return true;
            }
            return false;
        });

        // 初始焦点设置在二维码输入框
        qrCodeInput.requestFocus();

        mScanDialog = builder.create();

        // 设置对话框显示后的焦点处理
        mScanDialog.setOnShowListener(dialog -> {
            qrCodeInput.requestFocus();
        });

        mScanDialog.show();
    }

    private void handleScanResult(String qrCode) {
        Log.d("coco","hxx---handleScanResult--handleScanResult-----: " + qrCode);
        Log.d("coco","hxx---handleScanResult--EXPECTED_QR_CODE : " + EXPECTED_QR_CODE);
        if (mScanDialog != null && mScanDialog.isShowing()) {
            mScanDialog.dismiss();
        }
        boolean isMatch = EXPECTED_QR_CODE.equals(qrCode);
        mScannerBtn.setBackgroundColor(isMatch ? Color.GREEN : Color.RED);

        // 只在扫码功能启用时才需要检查扫码结果
        if (isScannerEnabled && isMatch && isNetworkTestPassed && isLedCheckPassed) {
            isAllTestsPassed = true;
            showSuccessDialog();
        }
    }

    private void startNetworkTest() {
        // 先检查充电状态
        IntentFilter ifilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatus = registerReceiver(null, ifilter);
        int status = batteryStatus.getIntExtra(android.os.BatteryManager.EXTRA_STATUS, -1);
        boolean isCharging = status == android.os.BatteryManager.BATTERY_STATUS_CHARGING ||
                status == android.os.BatteryManager.BATTERY_STATUS_FULL;

        if (!isCharging) {
            Toast.makeText(this, "请插入底座再测试", Toast.LENGTH_SHORT).show();
            mNetworkBtn.setBackgroundColor(Color.RED);
            return;
        }

        // 检查MAC地址是否可获取
        String macAddress = getMacAddr();
        if (macAddress.isEmpty()) {
            Toast.makeText(this, "请插入网线再测试", Toast.LENGTH_SHORT).show();
            mNetworkBtn.setBackgroundColor(Color.RED);
            return;
        }

        // 有以太网连接时进行网关ping测试
        new Thread(() -> {
            String gateways = getEthernetGateway();
            boolean canPingGateway = pingGateway(gateways);
            Log.d("coco","hxx-----canPingGateway : " + canPingGateway);
            Log.d("coco","hxx-----gateways : " + gateways);
            runOnUiThread(() -> {
                if (canPingGateway) {
                    isNetworkTestPassed = true;
                    showLedCheckDialog();
                } else {
                    mNetworkBtn.setBackgroundColor(Color.RED);
                    Toast.makeText(this, 
                        String.format("网口测试失败，无法ping通网关 %s", gateways),
                        Toast.LENGTH_SHORT).show();
                }
            });
        }).start();
    }

    private boolean isEthernetConnected() {
        try {
            NetworkInterface intf = NetworkInterface.getByName(ETHERNET_INTERFACE);
            if (intf == null) {
                return false;
            }
            
            // 检查网络接口是否启用且已连接
            return intf.isUp() && !intf.isLoopback() && intf.getInetAddresses().hasMoreElements();
        } catch (SocketException e) {
            Log.e("MainActivity", "Error checking ethernet interface", e);
            return false;
        }
    }

    private void showLedCheckDialog() {
        // 先关闭已存在的对话框
        if (mScanDialog != null && mScanDialog.isShowing()) {
            mScanDialog.dismiss();
            mScanDialog = null;
        }
        if (mLedCheckDialog != null && mLedCheckDialog.isShowing()) {
            mLedCheckDialog.dismiss();
            mLedCheckDialog = null;
        }
        
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = getLayoutInflater().inflate(R.layout.layout_dialog_led_check, null);

        Button passButton = dialogView.findViewById(R.id.btn_led_pass);
        Button failButton = dialogView.findViewById(R.id.btn_led_fail);

        passButton.setOnClickListener(v -> {
            isLedCheckPassed = true;
            updateNetworkButtonState();
            if (isScannerEnabled) {  // 扫码功能启用时显示扫码对话框
                showScanDialog();
            } else {  // 扫码功能未启时直接检查是否显示成功对话框
                checkAndShowSuccessDialog();
            }
            if (mLedCheckDialog != null) {
                mLedCheckDialog.dismiss();
            }
        });

        failButton.setOnClickListener(v -> {
            isLedCheckPassed = false;
            updateNetworkButtonState();
            //showScanDialog();
            if (mLedCheckDialog != null) {
                mLedCheckDialog.dismiss();
            }
        });

        builder.setTitle("网口指示灯检查")
               .setMessage("插入网线，并检查网口的黄色和绿色指示灯是否都已亮起？")
               .setView(dialogView)
               .setCancelable(false);

        mLedCheckDialog = builder.create();
        mLedCheckDialog.show();
    }

    private void updateNetworkButtonState() {
        mNetworkBtn.setBackgroundColor(
                (isNetworkTestPassed && isLedCheckPassed) ? Color.GREEN : Color.RED);
    }

    @Override
    protected void onPause() {
        super.onPause();
        if(mScanDialog != null){
            mScanDialog.dismiss();
            mScanDialog = null;
        }
        Log.d("coco","hxx----onPause----");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mChargingReceiver != null) {
            unregisterReceiver(mChargingReceiver);
        }
        if (mEthernetReceiver != null) {
            unregisterReceiver(mEthernetReceiver);
        }
        if (mSuccessDialog != null) {
            mSuccessDialog.dismiss();
            mSuccessDialog = null;
        }
        if (mAgingSettingsDialog != null) {
            mAgingSettingsDialog.dismiss();
            mAgingSettingsDialog = null;
        }
        Log.d("coco","hxx----onDestroy----");
    }

    private void showAgingSettingsDialog() {
        Log.d("AgingTest", "开始显示老化设置对话框");
        // 如果对话框已存在且正在显示，直接返回
        if (mAgingSettingsDialog != null && mAgingSettingsDialog.isShowing()) {
            Log.d("AgingTest", "对话框已经在显示中");
            return;
        }

        // 使用异步方式检查充电状态
        new Thread(() -> {
            IntentFilter ifilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
            Intent batteryStatus = registerReceiver(null, ifilter);
            int status = batteryStatus.getIntExtra(android.os.BatteryManager.EXTRA_STATUS, -1);
            boolean isCharging = status == android.os.BatteryManager.BATTERY_STATUS_CHARGING ||
                    status == android.os.BatteryManager.BATTERY_STATUS_FULL;
            
            runOnUiThread(() -> {
                if (!isCharging) {
                    Toast.makeText(MainActivity.this, "请连接充电器后再进行老化测试", Toast.LENGTH_SHORT).show();
                    return;
                }

                // 重置选项状态
                if (mAgingLevelGroup != null) {
                    mAgingLevelGroup.check(R.id.rb_high_level);
                }
                if (mAgingDurationSpinner != null) {
                    mAgingDurationSpinner.setSelection(0);
                }

                // 显示对话框
                if (mAgingSettingsDialog != null && !mAgingSettingsDialog.isShowing()) {
                    Window window = mAgingSettingsDialog.getWindow();
                    if (window != null) {
                        // 禁用窗口动画
                        window.setWindowAnimations(R.style.DialogNoAnimation);
                    }
                    mAgingSettingsDialog.show();
                }
            });
        }).start();
    }

    private void showSuccessDialog() {
        if (mSuccessDialog != null && mSuccessDialog.isShowing()) {
            mSuccessDialog.dismiss();
        }

        // 获取MAC地址
        String macAddress = getMacAddr();
        
        // 检查MAC地址是否为空
        if (macAddress.isEmpty()) {
            AlertDialog.Builder errorBuilder = new AlertDialog.Builder(this);
            errorBuilder.setCustomTitle(createCustomTitle("错误提示"))
                       .setMessage("获取底座MAC地址失败，请：\n1. 确认已插入底座/网线\n2. 确认网口是否已写入MAC")
                       .setPositiveButton("确定", null)
                       .show();
            return;
        }

        // 如果MAC地址变化或缓存的二维码为空，则重新生成二维码
        String plainMacAddress = macAddress.replace(":", "");
        if (mCachedQRCode == null || !plainMacAddress.equals(mCachedMacAddress)) {
            generateQRCode(plainMacAddress);
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = getLayoutInflater().inflate(R.layout.layout_dialog_success, null);

        ImageView ivQrCode = dialogView.findViewById(R.id.iv_qr_code);
        TextView tvCode = dialogView.findViewById(R.id.tv_success_code);

        // 使用缓存的二维码
        if (mCachedQRCode != null) {
            ivQrCode.setImageBitmap(mCachedQRCode);
            ivQrCode.setScaleType(ImageView.ScaleType.FIT_CENTER);
        }

        // 设置MAC地址文本
        tvCode.setText("MAC: " + plainMacAddress);
        tvCode.setVisibility(View.VISIBLE);

        builder.setView(dialogView)
               .setCancelable(true);
        
        mSuccessDialog = builder.create();
        mSuccessDialog.show();
    }

    // 新增生成二维码的方法
    private void generateQRCode(String content) {
        try {
            QRCodeWriter writer = new QRCodeWriter();
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.MARGIN, 1);

            BitMatrix bitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, 250, 250, hints);
            int width = bitMatrix.getWidth();
            int height = bitMatrix.getHeight();
            
            // 使用RGB_565格式来减少内存占用
            mCachedQRCode = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
            
            for (int x = 0; x < width; x++) {
                for (int y = 0; y < height; y++) {
                    mCachedQRCode.setPixel(x, y, bitMatrix.get(x, y) ? Color.BLACK : Color.WHITE);
                }
            }
            
            // 缓存当前MAC地址
            mCachedMacAddress = content;
        } catch (Exception e) {
            e.printStackTrace();
            mCachedQRCode = null;
            mCachedMacAddress = null;
        }
    }

    private View createCustomTitle(String title) {
        TextView titleView = new TextView(this);
        titleView.setText(title);
        titleView.setTextColor(Color.RED); // 设置文本颜色为红色
        titleView.setTextSize(20); // 设置字体大小
        return titleView;
    }

    // 新增获取以太网MAC地址的方法
    public String getMacAddr() {
        try {
            Process process = Runtime.getRuntime().exec("ifconfig eth0");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            
            while ((line = reader.readLine()) != null) {
                Log.d("coco", "hxx-----读取行: " + line);
                if (line.contains("HWaddr")) {
                    // 提取MAC地址
                    String[] parts = line.split("HWaddr");
                    if (parts.length > 1) {
                        // 提取MAC地址并去除Driver信息
                        String[] macParts = parts[1].trim().split("\\s+");
                        String mac = macParts[0].trim();
                        Log.d("coco", "hxx-----成功获取HWaddr: " + mac);
                        return mac;
                    }
                }
            }
            Log.d("coco", "hxx-----未找到HWaddr信息");
        } catch (Exception ex) {
            Log.e("coco", "hxx-----获取MAC地址时发生异常", ex);
            ex.printStackTrace();
        }
        return "";
    }

    private void checkAndShowSuccessDialog() {
        // 当扫码功能未启用时,只需要检查充电和网络测试
        if (isNetworkTestPassed && isLedCheckPassed) {
            isAllTestsPassed = true;
            showSuccessDialog();
        }
    }

    // 获取以太网网关地址
    private String getEthernetGateway() {
        // 主网关和备用网关
        return "192.168.10.1,192.168.1.1";
    }

    // ping网关
    private boolean pingGateway(String gateways) {
        if (gateways.isEmpty()) {
            return false;
        }

        // 分割多个网关地址
        String[] gatewayArray = gateways.split(",");

        for (String gateway : gatewayArray) {
            try {
                Process process = Runtime.getRuntime().exec("ping -c 1 -W 1 " + gateway.trim());
                int exitValue = process.waitFor();
                if (exitValue == 0) {
                    Log.d("MainActivity", "成功ping通网关: " + gateway);
                    return true;
                }
                Log.d("MainActivity", "ping网关失败: " + gateway);
            } catch (Exception e) {
                Log.e("MainActivity", "ping网关出错: " + gateway, e);
            }
        }
        return false;
    }

    // 通过反射获取系统属性
    private String getSystemProperty(String key, String defaultValue) {
        try {
            Class<?> systemProperties = Class.forName("android.os.SystemProperties");
            Method get = systemProperties.getMethod("get", String.class, String.class);
            return (String) get.invoke(null, key, defaultValue);
        } catch (Exception e) {
            e.printStackTrace();
            return defaultValue;
        }
    }

    // 预先初始化老化测试对话框
    private void initAgingDialog() {
        if (mAgingDialogView == null) {
            mAgingDialogView = getLayoutInflater().inflate(R.layout.layout_dialog_aging_settings, null);
            mAgingLevelGroup = mAgingDialogView.findViewById(R.id.rg_aging_level);
            mAgingDurationSpinner = mAgingDialogView.findViewById(R.id.spinner_duration);

            // 初始化时长选项适配器
            mDurationAdapter = new ArrayAdapter<>(this,
                    android.R.layout.simple_spinner_item, AGING_DURATIONS);
            mDurationAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            mAgingDurationSpinner.setAdapter(mDurationAdapter);

            // 创建对话框并预配置
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setView(mAgingDialogView)
                    .setTitle("老化测试设置")
                    .setPositiveButton("开始", null)
                    .setNegativeButton("取消", (dialog, which) -> dialog.dismiss());

            mAgingSettingsDialog = builder.create();
            
            // 预先设置窗口属性
            Window window = mAgingSettingsDialog.getWindow();
            if (window != null) {
                window.setWindowAnimations(R.style.DialogNoAnimation);
            }

            // 设置按钮点击事件
            mAgingSettingsDialog.setOnShowListener(dialogInterface -> {
                Button positiveButton = mAgingSettingsDialog.getButton(AlertDialog.BUTTON_POSITIVE);
                positiveButton.setOnClickListener(v -> {
                    String level = mAgingLevelGroup.getCheckedRadioButtonId() == R.id.rb_high_level ? "high" : "low";
                    int position = mAgingDurationSpinner.getSelectedItemPosition();
                    long durationMinutes = AGING_DURATION_MINUTES[position];

                    Intent intent = new Intent(MainActivity.this, AgingTestActivity.class);
                    intent.putExtra("duration", durationMinutes);
                    intent.putExtra("level", level);
                    startActivity(intent);

                    mAgingSettingsDialog.dismiss();
                });
            });
        }
    }

    /**
     * 显示权限授予状态的Toast提示
     */
    private void showPermissionStatusToast() {
        new android.os.Handler().postDelayed(() -> {
            // 使用新的权限检查工具
            PermissionChecker.showPermissionStatusToast(this);

            // 生成详细的权限报告并记录到日志
            String report = PermissionChecker.generatePermissionReport(this);
            Log.i("MainActivity", "详细权限报告:\n" + report);

        }, 2000); // 延迟2秒显示，确保权限授予完成
    }

}